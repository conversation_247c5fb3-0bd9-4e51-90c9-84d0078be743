﻿<script type="text/x-template" id="classification-display-template">

    <div class="form-group-display">
        <label>Classification Category</label><br />
        <span>{{referenceClassification.classificationCategoryName == null ? 'N/A' : referenceClassification.classificationCategoryName}}</span>
    </div>

    <div class="form-group-display">
        <label>Minimal Criteria</label><br />
        <span>{{referenceClassification.minimalCriteria == '' ? 'N/A' : referenceClassification.minimalCriteria}}</span>
    </div>

    <div class="form-group-display">
        <label>Country of Occurrence</label><br />
        <span>{{referenceClassification.countryOfOccurrence == null ? 'N/A' : referenceClassification.countryOfOccurrence}}</span>
    </div>

    <div class="form-group-display">
        <label>PSUR Relevant</label><br />
        <span>{{referenceClassification.psurRelevanceAbstract == null ? 'N/A' : referenceClassification.psurRelevanceAbstract}}</span>
    </div>

    <div class="form-group-display">
        <label>PV Safety Database ID</label><br />
        <span :style="[referenceClassification.pvSafetyDatabaseId == null ? 'font-style: italic;' : '']">
            {{referenceClassification.pvSafetyDatabaseId == null ? 'None' : referenceClassification.pvSafetyDatabaseId}}
        </span>
    </div>

    <div class="form-group-display">
        <label>Dosage Form</label><br />
        <span>{{referenceClassification.dosageForm == null ? 'N/A' : referenceClassification.dosageForm}}</span>
    </div>

    <div v-if="referenceClassification.referenceStateText == 'Reclassified'" class="form-group-display">
        <label>Reason for update</label><br />
        <span class="reason-for-change" >{{referenceClassification.reasonForChange}}</span>
    </div>

</script>

<script type="text/javascript">
    vueApp.component('classification-display', {
        template: '#classification-display-template',
        props: {
            referenceClassification: {
                type: Object
            }
        }
    });
</script>

