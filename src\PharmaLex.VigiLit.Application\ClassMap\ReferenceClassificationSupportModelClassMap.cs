﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.ClassMap;

public sealed class ReferenceClassificationSupportModelClassMap : CamelCaseClassMap<ReferenceClassificationSupportModel>
{
    public ReferenceClassificationSupportModelClassMap()
    {
        Map(m => m.Id).Name("PLX ID");
        Map(m => m.Doi).Name("DOI");
        Map(m => m.MeshTerms).Name("Mesh Terms");
        Map(m => m.AffiliationTextFirstAuthor).Name("Affiliation");
        Map(m => m.Issn).Name("ISSN");
        Map(m => m.ClassificationCategory).Name("Classification Category");
        Map(m => m.PSURRelevanceAbstract).Name("PSUR Relevant");
        Map(m => m.CreatedDate).Name("Created On");
        Map(m => m.LastUpdatedDate).Name("Modified");
    }
}