﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class QualityControlChecker : IQualityControlChecker
{
    private readonly float _mandatoryFieldMinimumConfidenceLevel;
    private readonly float _nonMandatoryFieldMinimumConfidenceLevel;
    private readonly float _overallMinimumConfidenceLevel;
    private readonly float _mandatoryWeight;
    private readonly float _nonMandatoryWeight;

    private readonly List<PropertyInfo> _mandatoryFields = [];
    private readonly List<PropertyInfo> _nonMandatoryFields = [];
    private readonly ILogger<QualityControlChecker> _logger;

    public QualityControlChecker(ILoggerFactory loggerFactory, IConfiguration configuration)
    {
        PopulatePropertyInfoCollections();

        _mandatoryFieldMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:MandatoryFieldMinimumConfidenceLevel");
        _nonMandatoryFieldMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:NonMandatoryFieldMinimumConfidenceLevel");
        _overallMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:OverallMinimumConfidenceLevel");
        _mandatoryWeight = configuration.GetValue<float>("DataExtraction:MandatoryWeight");
        _nonMandatoryWeight = configuration.GetValue<float>("DataExtraction:NonMandatoryWeight");
        _logger = loggerFactory.CreateLogger<QualityControlChecker>();
    }

    public bool DoesPassQualityControl(ExtractedReference reference)
    {
        return DoMandatoryFieldsHaveValues(reference) &&
               DoMandatoryFieldsHaveHighConfidence(reference) &&
               DoNonMandatoryFieldsMeetConfidenceLevel(reference) &&
               DoesMeetOverallWeightedConfidence(reference);
    }

    private bool DoMandatoryFieldsHaveValues(ExtractedReference reference)
    {
        foreach (var mandatoryField in _mandatoryFields)
        {
            var extractedProperty = mandatoryField.GetValue(reference) as IExtractedProperty;
            if (extractedProperty is null || IsEmptyOrWhiteSpace(extractedProperty.Value))
            {
                _logger.LogInformation("Mandatory property: {mandatoryFieldName} is empty", mandatoryField.Name);
                return false;
            }
        }

        return true;
    }

    private bool DoMandatoryFieldsHaveHighConfidence(ExtractedReference reference)
    {
        return DoFieldsPassConfidenceLevel(reference, _mandatoryFields, _mandatoryFieldMinimumConfidenceLevel);
    }

    private bool DoNonMandatoryFieldsMeetConfidenceLevel(ExtractedReference reference)
    {
        return DoFieldsPassConfidenceLevel(reference, _nonMandatoryFields, _nonMandatoryFieldMinimumConfidenceLevel);
    }

    private static bool DoFieldsPassConfidenceLevel(ExtractedReference reference, List<PropertyInfo> fields, float confidenceLevel)
    {
        foreach (var field in fields)
        {
            if (field.GetValue(reference) is ICollection<IExtractedProperty> items)
            {
                if (items.AnyFailConfidence(confidenceLevel))
                {
                    Console.WriteLine($"Property: {field.Name} has low confidence.");
                    return false;
                }
            }
            else
            {
                var extractedProperty = field.GetValue(reference) as IExtractedProperty;
                if (extractedProperty is null || extractedProperty.FailConfidence(confidenceLevel))
                {
                    Console.WriteLine($"Property: {field.Name} has low confidence.");
                    return false;
                }
            }
        }

        return true;
    }

    private bool DoesMeetOverallWeightedConfidence(ExtractedReference reference)
    {
        var nonMandatoryConfidences = GetConfidenceLevels(reference, _nonMandatoryFields);

        var mandatoryWeightedSum = _mandatoryFields.Sum(x => (x.GetValue(reference) as IExtractedProperty)!.Confidence * _mandatoryWeight);
        var nonMandatoryWeightedSum = nonMandatoryConfidences.Sum(confidence => confidence * _nonMandatoryWeight);
        var totalWeight = _mandatoryFields.Count * _mandatoryWeight + _nonMandatoryFields.Count * _nonMandatoryWeight;
        var overallConfidence = totalWeight <= 0.001F ? 0.0F : (mandatoryWeightedSum  + nonMandatoryWeightedSum) / totalWeight;

        return overallConfidence > _overallMinimumConfidenceLevel;
    }

    private static List<float> GetConfidenceLevels(ExtractedReference reference, List<PropertyInfo> fields)
    {
        var confidenceLevels = new List<float>();
        foreach (var field in fields)
        {
            if (field.GetValue(reference) is ICollection<IExtractedProperty> items)
            {
                confidenceLevels.AddRange(items.Select(x => x.Confidence));
            }
            else
            {
                var extractedProperty = field.GetValue(reference) as IExtractedProperty;
                confidenceLevels.Add(extractedProperty!.Confidence);
            }
        }
        return confidenceLevels;
    }

    private void PopulatePropertyInfoCollections()
    {
        var properties = typeof(ExtractedReference).GetProperties();
        foreach (var info in properties)
        {
            if (Attribute.IsDefined(info, typeof(MandatoryAttribute)))
            {
                _mandatoryFields.Add(info);
            }
            else
            {
                _nonMandatoryFields.Add(info);
            }
        }
    }

    private static bool IsEmptyOrWhiteSpace(string value)
    {
        return value.All(char.IsWhiteSpace);
    }
}