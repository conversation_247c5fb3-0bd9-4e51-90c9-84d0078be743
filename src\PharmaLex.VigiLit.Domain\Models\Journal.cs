﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.Models;

public class Journal : VigiLitEntityBase
{
    public string Name { get; set; }

    public int CountryId { get; set; }

    public string Url { get; set; }

    public Country Country { get; set; }

    public bool Enabled { get; set; }

    /// <summary>
    /// Gets or sets the ISSN.
    /// </summary>
    /// <remarks>
    /// Conforms to ISO-3297 <see href=" https://en.wikipedia.org/wiki/ISSN">See here</see>
    /// </remarks>
    /// <value>
    /// The ISSN.
    /// </value>
    [StringLength(9)]
    public string Issn { get; set; }

    [MaxLength(50)]
    [Column(TypeName = "varchar(50)")]
    public string CronExpression { get; set; }

    /// <summary>
    /// Gets or sets the date and time of the last scraping run (successful or failed).
    /// </summary>
    public DateTime? LastRun { get; set; }

    public ICollection<ContractVersionJournal> ContractVersionJournals { get; set; } = new List<ContractVersionJournal>();
}