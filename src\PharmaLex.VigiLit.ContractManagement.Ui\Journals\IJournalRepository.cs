﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public interface IJournalRepository : ITrackingRepository<Journal>
{
    Task<IEnumerable<Journal>> GetAll();
    Task<IEnumerable<Journal>> GetForCountry(int countryId);
    Task<Journal?> GetById(int id);

    /// <summary>
    /// Gets the countries for which there are subscriptions to journals.
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<Country>> GetSubscribedCountries();

    Task<int> GetContractsWithEnabledJournalById(int journalId);

    /// <summary>
    /// Updates the LastRun timestamp for a journal by its ID.
    /// </summary>
    /// <param name="journalId">The journal ID.</param>
    /// <param name="lastRunTime">The timestamp of the last run.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task UpdateLastRun(int journalId, DateTime lastRunTime);
}