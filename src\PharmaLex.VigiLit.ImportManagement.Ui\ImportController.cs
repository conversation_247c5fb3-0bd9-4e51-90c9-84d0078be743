﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.Core.Web.Enums;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;
using PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;
using PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.ManualEntry;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;
using PharmaLex.VigiLit.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Security.Cryptography;
using Microsoft.FeatureManagement;

namespace PharmaLex.VigiLit.ImportManagement.Ui;

[Authorize(Policy = Policies.Admin)]
[Route("[controller]")]
public class ImportController : BaseController
{
    private readonly IImportService _importService;
    private readonly IImportQueueService _importQueueService;
    private readonly ICompanyService _companyService;
    private readonly IProjectService _projectService;
    private readonly IContractService _contractService;
    private readonly IReferenceService _referenceService;
    private readonly IAdHocService _adHocService;
    private readonly IGenericCardService<ImportManualEntry, ManualEntryModel> _manualEntryService;
    private readonly IImportFileService _importFileService;
    private readonly IGenericCardService<FailedImportFile, FailedImportFileModel> _failedImportService;
    private readonly IFeatureManager _featureManager;
    private readonly ILogger<ImportController> _logger;
    private const string EnableManualEntry = "EnableManualEntry";
    private const string EnableFileImportManualEntry = "EnableFileImportManualEntry";

    [SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "Configured limit is 7, we need 8 here and that's ok.")]
    public ImportController(IImportService importService,
        IImportQueueService importQueueService,
        ICompanyService companyService,
        IProjectService projectService,
        IContractService contractService,
        IUserSessionService userSessionService,
        IConfiguration configuration,
        IReferenceService referenceService,
        IAdHocService adHocService,
        IGenericCardService<ImportManualEntry, ManualEntryModel> manualEntryService,
        IImportFileService importFileService,
        IGenericCardService<FailedImportFile, FailedImportFileModel> failedImportService,
        IFeatureManager featureManager,
        ILogger<ImportController> logger) : base(userSessionService, configuration)
    {
        _importService = importService;
        _importQueueService = importQueueService;
        _companyService = companyService;
        _projectService = projectService;
        _contractService = contractService;
        _referenceService = referenceService;
        _adHocService = adHocService;
        _manualEntryService = manualEntryService;
        _importFileService = importFileService;
        _failedImportService = failedImportService;
        _featureManager = featureManager;
        _logger = logger;
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> ImportLog()
    {
        var data = await _importService.GetImportLog();
        return View(data);
    }

    [HttpGet("[action]/{importId}")]
    public async Task<IActionResult> ImportLogDetails(int importId)
    {
        var data = new ImportLogDetailsPageModel()
        {
            ImportId = importId,
            ImportContractModels = await _importService.GetImportContractsLog(importId),
            CanRetry = await _importQueueService.CanRetry(importId)
        };
        return View(data);
    }

    [HttpPost("[action]/{importId}")]
    public async Task<IActionResult> ImportLogDetails_Retry(int importId)
    {
        if (importId <= 0)
        {
            return BadRequest("Invalid ImportId.");
        }

        _logger.LogInformation("Retry added to queue with Id: {ImportId}", importId);
        await _importQueueService.Retry(importId);

        AddNotification("Retry added to queue.", UserNotificationType.Confirm);

        return RedirectToAction("ImportLog");
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> AdHocList()
    {
        var displayCards = new List<ImportDisplayCard>();
        var fileCards = new List<ImportDisplayCard>();
        var failedImportCards = new List<ImportDisplayCard>();
        var manualCards = new List<ImportDisplayCard>();
        var adHocCards = await _adHocService.GetCards();

        if (await _featureManager.IsEnabledAsync(EnableFileImportManualEntry))
        {
            fileCards = await _importFileService.GetCards();
            failedImportCards = await _failedImportService.GetCards();
        }

        if (await _featureManager.IsEnabledAsync(EnableManualEntry))
        {
            manualCards = await _manualEntryService.GetCards();
        }
        displayCards.AddRange(adHocCards);
        displayCards.AddRange(fileCards);
        displayCards.AddRange(manualCards);
        displayCards.AddRange(failedImportCards);

        var result = new ImportQueuePageModel()
        {
            ImportDisplayCards = displayCards,
        };
        return View(result);
    }

    [HttpGet("[action]/{adHocImportId}")]
    public async Task<IActionResult> AdHocListDetails(int adHocImportId)
    {
        var data = new AdHocListDetailsPageModel()
        {
            AdHocImportId = adHocImportId,
            AdHocImportContractModels = await _importService.GetAdHocImportContractsList(adHocImportId),
        };
        return View(data);
    }

    [HttpGet("[action]")]
    public IActionResult FileImport()
    {
        return View("FileImport");
    }

    [HttpPost("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> FileImport(IList<IFormFile> files, [FromQuery] Guid batchId)
    {
        if (files == null || files.Count == 0)
        {
            return BadRequest("No files provided.");
        }

        try
        {
            foreach (var file in files)
            {
                if (file == null)
                {
                    return BadRequest($"file doesn't exist");
                }

                await using var stream = file.OpenReadStream();
                var fileHash = GenerateSha256(stream);
                var importFile = new ImportFileModel(file.FileName, batchId, fileHash)
                {
                    FileName = file.FileName,
                    BatchId = batchId,
                    FileHash = fileHash
                };
                var model = new ImportFileUploadModel()
                {
                    ImportFile = importFile,
                    Stream = stream,
                };
                await _importFileService.Add(model);
            }

            return Ok();
        }
        catch (DbUpdateException)
        {
            return StatusCode(409, "The file appears to have already been uploaded");
        }
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> UpdateImportFile([FromQuery] Guid batchId)
    {
        var importFiles = await _importFileService.GetImportFile(batchId);
        return View("FileImport", importFiles);
    }

    [HttpDelete("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteImportFilesBatch([FromQuery] Guid batchId)
    {
        try
        {
            await _importFileService.Abandon(batchId);
            return Ok();
        }

        catch (KeyNotFoundException)
        {
            return NotFound($"No file found with the given ID: {batchId}");
        }
        catch (Exception ex)
        {
            AddNotification($"An error occurred while deleting the file {ex.Message}", UserNotificationType.Failed);
        }
        return Ok();
    }

    [HttpDelete("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteImportFile([FromBody] ImportFileModel model)
    {
        if (model == null || string.IsNullOrWhiteSpace(model.FileName))
        {
            return BadRequest("Invalid request. File name is required.");
        }

        try
        {
            await _importFileService.DeleteImportFile(model);
        }
        catch (KeyNotFoundException)
        {
            return NotFound($"No file found with the given file name: {model.FileName}");
        }
        catch (Exception ex)
        {
            AddNotification($"An error occurred while deleting file: {ex.Message}", UserNotificationType.Failed);
            return StatusCode(500, "An unexpected error occurred while deleting the file.");
        }

        return Ok("File deleted successfully.");
    }

    [HttpDelete("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteImportFiles([FromBody] List<ImportFileModel> models)
    {
        if (models == null || models.Count == 0 || models.Any(m => string.IsNullOrWhiteSpace(m.FileName)))
        {
            return BadRequest("Invalid request. At least one valid file name is required.");
        }
        try
        {
            await _importFileService.DeleteImportFiles(models);
        }
        catch (InvalidOperationException ex)
        {
            var batchId = new Guid(models[0].BatchId.ToString());
            var fileNames = string.Join(", ", models.Select(m => LogSanitizer.Sanitize(m.FileName)));
            _logger.LogInformation(ex, "An error occurred while deleting files: {FileNames} (Batch: {BatchId}). Error: {ErrorMessage}", fileNames, batchId, ex.Message);
            AddNotification($"An error occurred while deleting files: {fileNames} (Batch: {batchId})", UserNotificationType.Failed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unexpected error occurred while deleting files.");
            AddNotification("An unexpected error occurred while deleting files.", UserNotificationType.Failed);
        }

        return Ok("Files deleted successfully.");
    }

    [HttpGet("[action]/{batchId}/{fileName}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DownloadImportedFile(Guid batchId, string fileName)
    {
        try
        {
            var downloadFile = await _importFileService.DownloadImportedFile(batchId, fileName);
            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpGet("[action]/{batchId}/{fileName}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DownloadFailedImportFile(Guid batchId, string fileName)
    {
        try
        {
            var downloadFile = await ((IDownloadable<DownloadFile>)_failedImportService).Download(batchId, fileName);
            return File(downloadFile.Bytes, downloadFile.ContentType, downloadFile.FileName);
        }
        catch (UnauthorizedAccessException)
        {
            return BadRequest();
        }
    }

    [HttpPost("[action]/{adHocImportId}")]
    public async Task<IActionResult> AdHocListDetails_Abandon(int adHocImportId)
    {
        if (adHocImportId <= 0)
        {
            return BadRequest("Invalid AdHocImportId.");
        }

        _logger.LogInformation("Import with Id: {ImportId} abandoned", adHocImportId);
        await _adHocService.Abandon(adHocImportId);

        AddNotification("Abandoned.", UserNotificationType.Confirm);

        return Ok();
    }

    [HttpPost("[action]/{batchId}")]
    public async Task<IActionResult> ImportFile_Abandon(Guid batchId)
    {
        _logger.LogInformation("File Import with Batch Id: {batchId} abandoned", batchId);
        await _importFileService.Abandon(batchId);
        AddNotification("Abandoned.", UserNotificationType.Confirm);

        return Ok();
    }

    [HttpPost("[action]/{adHocImportId}")]
    public async Task<IActionResult> AdHocListDetails_Enqueue(int adHocImportId)
    {
        if (adHocImportId <= 0)
        {
            return BadRequest("Invalid AdHocImportId.");
        }

        _logger.LogInformation("Enqueuing import with Id: {ImportId}", adHocImportId);
        await _importQueueService.EnqueueAdHocImport(adHocImportId);

        AddNotification("Enqueued.", UserNotificationType.Confirm);

        return Ok();
    }

    [HttpGet("[action]")]
    public IActionResult AdHocCreate()
    {
        return View();
    }

    [HttpPost("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateImportReference([FromBody] ManualEntryModel model)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new { errors = ModelState });
        }

        var doiValidationResult = await ValidateDoi(model.Doi ?? string.Empty, model.Id);
        if (doiValidationResult is BadRequestObjectResult badRequestResult)
        {
            return badRequestResult;
        }

        try
        {
            await _manualEntryService.Add(model);
            AddNotification($"Import reference was created successfully", UserNotificationType.Confirm);
        }
        catch (Exception ex)
        {
            AddNotification($"An issue occurred while creating {ex.Message}", UserNotificationType.Failed);
        }

        return Ok();
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetImportReference(int id)
    {
        try
        {
            var manualEntry = await _manualEntryService.Get(id);
            if (manualEntry == null)
            {
                return NotFound();
            }
            return Ok(manualEntry);
        }
        catch (Exception)
        {
            return BadRequest("Error retrieving data");
        }
    }

    [HttpPost("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateImportReference([FromBody] ManualEntryModel model)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new { errors = ModelState });
        }

        var doiValidationResult = await ValidateDoi(model.Doi ?? string.Empty, model.Id);
        if (doiValidationResult is BadRequestObjectResult badRequestResult)
        {
            return badRequestResult;
        }

        try
        {
            await _manualEntryService.Update(model);
            AddImportReferenceNotification("Manual Entry", model.StatusType);
        }
        catch (Exception ex)
        {
            AddNotification($"An issue occurred while editing {ex.Message}", UserNotificationType.Failed, 3500);

        }
        return Ok();
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetFailedImportFile(int id)
    {
        try
        {
            var failedImportFile = await _failedImportService.Get(id);
            if (failedImportFile == null)
            {
                return NotFound();
            }
            return Ok(failedImportFile);
        }
        catch (Exception)
        {
            return BadRequest("Error retrieving data");
        }
    }

    [HttpPost("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateFailedImportFile([FromBody] FailedImportFileModel model)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(new { errors = ModelState });
        }

        var doiValidationResult = await ValidateDoi(model.Doi ?? string.Empty, model.Id);
        if (doiValidationResult is BadRequestObjectResult badRequestResult)
        {
            return badRequestResult;
        }

        try
        {
            await _failedImportService.Update(model);
            AddImportReferenceNotification("Failed Import File", model.StatusType);
        }
        catch (Exception ex)
        {
            AddNotification($"An issue occurred while editing {ex.Message}", UserNotificationType.Failed, 3500);

        }
        return Ok();
    }


    [HttpPost("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EnqueueImportReference([FromBody] ImportDisplayCard model)
    {
        switch (model.ImportType)
        {
            case "Manual":
                await _manualEntryService.Enqueue(int.Parse(model.Id));
                AddImportReferenceNotification(model.ImportType, ImportReferenceStatusType.Queued);
                break;
            case "File":
                await _importFileService.Enqueue(Guid.Parse(model.Id));
                AddImportReferenceNotification(model.ImportType, ImportReferenceStatusType.Queued);
                break;
            case "Failed":
                await _failedImportService.Enqueue(int.Parse(model.Id));
                AddImportReferenceNotification(model.ImportType, ImportReferenceStatusType.Queued);
                break;
        }

        return Ok();
    }

    [HttpDelete("[action]/{id}")]
    public async Task<IActionResult> AbandonManual(int id)
    {
        await _manualEntryService.Abandon(id);
        return Ok();
    }

    [HttpDelete("[action]/{id}")]
    public async Task<IActionResult> AbandonFailed(int id)
    {
        await _failedImportService.Abandon(id);
        return Ok();
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> CheckIfDoiIsValid([FromQuery] string doi)
    {
        if (string.IsNullOrEmpty(doi))
        {
            return Ok(true);
        }

        var model = await _referenceService.IsDoiValid(doi);
        return Ok(model);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> CheckIfDoiExist([FromQuery] string doi, int id)
    {
        var isInReference = await _referenceService.IsDoiExisting(doi);

        var manualEntry = await ((IHasSearchByDoi<ImportManualEntry>)_manualEntryService).GetByDoi(doi);
        var isInManualEntry = manualEntry != null && manualEntry.Id != id;
        var doiExists = isInManualEntry || isInReference;
        return Ok(doiExists);
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AdHocCreate_GetCompanies()
    {
        var companies = await _companyService.GetAllAsync();
        return Json(companies);
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AdHocCreate_GetProjects(int companyId)
    {
        var projects = await _projectService.GetByCompanyIdAsync(companyId);
        return Json(projects);
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AdHocCreate_GetContracts(int companyId, int projectId)
    {
        var contracts = await _contractService.GetForAdHocImportCreate(companyId, projectId);
        return Json(contracts);
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> AdHocCreate_Save([FromBody] AdHocImportModel model)
    {
        await _adHocService.Add(model);
        AddNotification("Changes saved.", UserNotificationType.Confirm);
        return Ok();
    }

    private async Task<IActionResult> ValidateDoi(string doi, int id)
    {
        // Allow empty and NULL DOI
        if (string.IsNullOrEmpty(doi))
        {
            return Ok();
        }

        var isValidDoi = await _referenceService.IsDoiValid(doi);
        if (!isValidDoi)
        {
            return BadRequest($"{doi} is not a valid DOI");
        }

        var isInReference = await _referenceService.IsDoiExisting(doi);
        var manualEntry = await ((IHasSearchByDoi<ImportManualEntry>)_manualEntryService).GetByDoi(doi);
        var isInManualEntry = manualEntry != null && manualEntry.Id != id;

        if (isInManualEntry || isInReference)
        {
            return BadRequest($"DOI: {doi} already exists");
        }
        return Ok();
    }
    private void AddImportReferenceNotification(string cardType, ImportReferenceStatusType? statusType)
    {
        string message = statusType == ImportReferenceStatusType.Queued
            ? $"{cardType} enqueued successfully"
            : $"{cardType} updated successfully";

        AddNotification(message, UserNotificationType.Confirm);
    }

    private static string GenerateSha256(Stream stream)
    {
        using var sha256 = SHA256.Create();
        stream.Position = 0;
        var hashBytes = sha256.ComputeHash(stream);
        return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
    }
}