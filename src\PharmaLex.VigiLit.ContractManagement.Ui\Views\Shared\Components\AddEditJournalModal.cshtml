﻿<script type="text/x-template" id="journal-modal-template">
    <div id="journal-modal" v-cloak>
        <div class="modal fade" id="addJournalModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">Add Journal</h2>
                        <label class="switch-container switch-enabled-disabled">
                            <input class="switch" name="enabled" id="enabled" :value="journalObj.enabled" v-model="journalObj.enabled" @@change="onEnabledToggle" type="checkbox" aria-label="Enabled" />
                            <label class="switch" for="enabled"></label>
                        </label>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Data Source(URL)</label>
                            <input name="dataSource"
                                   v-model="journalObj.url"
                                   type="text"
                                   placeholder="Enter url"
                                   @@input="validateForm" />
                            <label v-if="errors.url" class="error-color mt-1">{{ errors.url }}</label>
                        </div>
                        <div class="form-group">
                            <label>Journal Title</label>
                            <input name="journalTitle"
                                   v-model="journalObj.name"
                                   placeholder="Enter journal title"
                                   type="text"
                                   @@input="validateForm" />
                            <label v-if="errors.name" class="error-color mt-1">{{ errors.name }}</label>
                        </div>
                        <div class="form-group">
                            <label>ISSN</label>
                            <input name="issn"
                                   v-model="journalObj.issn"
                                   placeholder="Enter ISSN"
                                   type="text"
                                   @@input="validateForm" />
                            <label v-if="errors.issn" class="error-color mt-1">{{ errors.issn }}</label>
                        </div>
                        <div class="form-group">
                            <label for="Country">Country</label>
                            <select v-model.number="journalObj.countryId" @@change="validateForm">
                                <option value="0" disabled>Select a country</option>
                                <option v-for="country in countries" :value="country.id">{{ country.name }}</option>
                            </select>
                            <label v-if="errors.countryId" class="error-color mt-1">{{ errors.countryId }}</label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="cancelBtn" class="button secondary icon-button-cancel btn-default" data-dismiss="modal">Cancel</button>
                        <button type="button" id="addEditBtn" class="btn btn-default" :disabled="isFormInvalid" @@click="save()"> Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <modal-dialog v-if="showWarningDialog"
                  :title="'Disable Journal Confirmation'"
                  :btntxt="'Ok'"
                  width="500px"
                  height="150px"
                  v-on:close="clearDialog"
                  v-on:confirm="confirm">
        <p>{{enabledJournalWarningMsg}}</p>
    </modal-dialog>
</script>
<script type="text/javascript">
    vueApp.component('journal-modal', {
        template: '#journal-modal-template',
        props: {
            journal: {
                type: Object,
                default: () => ({
                    url: '',
                    name: '',
                    countryId: 0,
                    enabled:true,
                    issn: ''
                })
            }
        },
        data() {
            return {
                journalObj: { ...this.journal },
                countries: [],
                showWarningDialog: false,
                enabledJournalWarningMsg:"",
                errors: {
                    url: '',
                    name: '',
                    countryId: '',
                    issn: ''
                }
            };
        },
        methods: {
            isValidUrl(url) {
                const urlPattern = /^(https?:\/\/)([\w-]+(\.[\w-]+)+)(\/[\w- ./?%&=]*)?$/i;
                return urlPattern.test(url);
            },
            isValidIssn(issn) {
                if (!issn) return false;

                // Remove hyphen and convert to uppercase
                issn = issn.replace('-', '').toUpperCase();

                // Must be 8 characters: 7 digits + 1 digit or 'X'
                if (!/^\d{7}[\dX]$/.test(issn)) return false;

                let sum = 0;
                for (let i = 0; i < 7; i++) {
                    sum += parseInt(issn[i], 10) * (8 - i);
                }

                let checkDigit = (11 - (sum % 11)) % 11;
                let expected = checkDigit === 10 ? 'X' : checkDigit.toString();

                return issn[7] === expected;
            },
            validateForm() {
                this.errors.name = (this.journalObj.name || '').trim() ? '' : '* Journal Title is required.';
                this.errors.countryId = this.journalObj.countryId > 0 ? '' : '* Please select a country.';
                this.errors.url =
                    this.journalObj.url && !this.isValidUrl(this.journalObj.url)
                        ? '* Invalid URL format.'
                        : '';
                this.errors.issn = 
                    this.journalObj.issn && !this.isValidIssn(this.journalObj.issn)
                        ? '* Invalid ISSN.'
                        : '';
            },
            save() {
                const updatedJournal = { ...this.journalObj };
                this.$emit('saved', {
                    journal: updatedJournal,
                    isEdit: this.isEdit
                });
            },
           onEnabledToggle() {
               if (!this.journalObj.enabled) {
                   this.GetEnabledJournalContractCount(this.journalObj.id);
                }
            },
            GetEnabledJournalContractCount(journalId) {
                fetch(`/Journals/GetEnabledJournalContractCount/${journalId}`, {
                    method: "GET",
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                })
                    .then(res => {
                        if (!res.ok) throw res;
                        return res.json();
                    })
                    .then(data => {
                        var count = data;
                        if (count > 0) {
                            this.showWarningDialog = true;
                            this.enabledJournalWarningMsg = `This journal is used in ${count} contract(s).`
                        }

                    })
                    .catch(() => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
            },
            clearDialog() {
                this.showWarningDialog = false;
                // Reset toggle back to enabled
                this.journalObj.enabled = true;
            },
            confirm() {
                this.showWarningDialog = false;
                this.journalObj.enabled = false;
            },
            getCountryList() {
                fetch(`/Journals/GetCountryList`, {
                    method: "GET",
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                })
                    .then(res => {
                        if (!res.ok) throw res;
                        return res.json();
                    })
                    .then(data => {
                        this.countries = data;
                    })
                    .catch(() => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
            },
            resetForm() {
                this.journalObj = {
                    url: '',
                    name: '',
                    countryId: 0,
                    issn: ''
                };
                this.validateForm();
            }
        },
        computed: {
            isFormInvalid() {
                return Object.values(this.errors).some(err => err);
            },
            isEdit() {
                return !!this.journalObj.id;
            }
        },
        watch: {
            journal: {
                handler(newVal) {
                    this.journalObj = { ...newVal };
                    this.validateForm(); // re-validate when prop changes
                },
                deep: true
            }

        },
        mounted() {
            this.validateForm();
            $('#addJournalModal').on('shown.bs.modal', () => {
                this.getCountryList();
            });
            $('#addJournalModal').on('hidden.bs.modal', () => {
                this.resetForm();
            });
        }
    });
</script>