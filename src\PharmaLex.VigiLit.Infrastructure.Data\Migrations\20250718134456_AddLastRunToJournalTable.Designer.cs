﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PharmaLex.VigiLit.DataAccessLayer;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    [DbContext(typeof(VigiLitDbContext))]
    [Migration("20250718134456_AddLastRunToJournalTable")]
    partial class AddLastRunToJournalTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ClaimUser", b =>
                {
                    b.Property<int>("ClaimsInternalId")
                        .HasColumnType("int");

                    b.Property<int>("UsersInternalId")
                        .HasColumnType("int");

                    b.HasKey("ClaimsInternalId", "UsersInternalId");

                    b.HasIndex("UsersInternalId");

                    b.ToTable("UserClaims", (string)null);
                });

            modelBuilder.Entity("PharmaLex.Core.UserSessionManagement.Entities.UserSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(39)
                        .HasColumnType("nvarchar(39)");

                    b.Property<string>("LastUpdatedBy")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("SessionData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("SessionExpiry")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserSessions", (string)null);
                });

            modelBuilder.Entity("PharmaLex.FeatureManagement.Entities.FeatureFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.HasKey("Id");

                    b.HasIndex("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("FeatureFlags", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.AiAnalysis.Entities.AiSuggestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar");

                    b.Property<string>("CategoryReason")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("DosageForm")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar");

                    b.Property<string>("DosageFormReason")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("SourceId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Substance")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar");

                    b.HasKey("Id");

                    b.ToTable("AISuggestions", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.DataExtraction.Entities.MdeQueueItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("DocumentLocation")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("Source")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("MdeQueueItem", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.AdHocImport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdHocImportStatusType")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SourceSearchEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SourceSearchStartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AdHocImportStatusType");

                    b.ToTable("AdHocImports", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.AdHocImportContract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdHocImportId")
                        .HasColumnType("int");

                    b.Property<int>("ContractId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("AdHocImportId");

                    b.HasIndex("ContractId");

                    b.ToTable("AdHocImportContracts", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.AiSuggestedClassificationStoredProcResult", b =>
                {
                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CategoryReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DosageForm")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DosageFormReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CaseCompanies", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CaseId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CaseId");

                    b.HasIndex("CompanyId");

                    b.ToTable("CaseCompanies", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CaseFileDocumentTypes", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Extension")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("MaxFileSizeMb")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.ToTable("CaseFileDocumentTypes", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CaseFiles", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CaseId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CaseId");

                    b.ToTable("CaseFiles", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Cases", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Comment")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("MLMDuplicate")
                        .HasColumnType("int");

                    b.Property<int>("ReferenceClassificationId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ReferenceClassificationId");

                    b.ToTable("Cases", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ClassificationCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<bool>("PushServiceRelevant")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("Id", "PushServiceRelevant");

                    b.ToTable("ClassificationCategories", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ClassificationCategoriesHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ContactPersonEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ContactPersonName")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Companies", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyInterest", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ReferenceClassificationId")
                        .HasColumnType("int");

                    b.Property<int>("ReferenceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ReferenceClassificationId");

                    b.HasIndex("ReferenceId");

                    b.HasIndex("CompanyId", "Id");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId", "Id"), new[] { "ReferenceId", "ReferenceClassificationId", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy" });

                    b.HasIndex("CompanyId", "ReferenceClassificationId")
                        .IsUnique();

                    b.HasIndex("CompanyId", "ReferenceId");

                    b.ToTable("CompanyInterests", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyReport", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ReportId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ReportId");

                    b.HasIndex("CompanyId", "ReportId")
                        .IsUnique();

                    b.ToTable("CompanyReports", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyUserType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(2);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("CompanyId", "UserId");

                    b.ToTable("CompanyUsers", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Contract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ContractStartDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GetUTCDate()");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CurrentContractVersionId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<int>("ScreeningType")
                        .HasColumnType("int");

                    b.Property<int>("SubstanceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("SubstanceId");

                    b.ToTable("Contracts", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ContractVersion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ContractId")
                        .HasColumnType("int");

                    b.Property<int>("ContractType")
                        .HasColumnType("int");

                    b.Property<int>("ContractVersionStatus")
                        .HasColumnType("int");

                    b.Property<int>("ContractWeekday")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ReasonForChange")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar");

                    b.Property<int>("SearchPeriodDays")
                        .HasColumnType("int");

                    b.Property<string>("SearchString")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<DateTime>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("ContractId", "ContractVersionStatus", "IsActive", "ContractType", "ContractWeekday");

                    b.ToTable("ContractVersions", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ContractVersionJournal", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ContractVersionId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("JournalId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ContractVersionId");

                    b.HasIndex("JournalId");

                    b.ToTable("ContractVersionJournals", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Country", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Iso")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar");

                    b.HasKey("Id");

                    b.HasIndex("Iso")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Countries", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Email", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("EmailStatusType")
                        .HasColumnType("int");

                    b.Property<int>("EmailTriggerType")
                        .HasColumnType("int");

                    b.Property<int>("EmailType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FailedEmailsCount")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("SentEmailsCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("StartDate");

                    b.ToTable("Emails", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("EmailId")
                        .HasColumnType("int");

                    b.Property<int>("EmailMessageStatusType")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("EmailId");

                    b.HasIndex("UserId");

                    b.ToTable("EmailMessages", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailMessageRelevantEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("EmailMessageId")
                        .HasColumnType("int");

                    b.Property<int>("EmailRelevantEventId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("EmailMessageId");

                    b.HasIndex("EmailRelevantEventId");

                    b.ToTable("EmailMessageRelevantEvents", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailPreference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("EmailPreferences", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailRelevantEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ClassificationCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("CompanyInterestId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("EmailReason")
                        .HasColumnType("int");

                    b.Property<int>("EmailRelevantEventActionType")
                        .HasColumnType("int");

                    b.Property<int>("EmailRelevantEventEmailStatusType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("EmailSentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ClassificationCategoryId");

                    b.HasIndex("CompanyInterestId");

                    b.HasIndex("EmailRelevantEventEmailStatusType", "CompanyInterestId");

                    b.ToTable("EmailRelevantEvents", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailSuppression", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<long>("Created")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("EmailSuppressionType")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("EmailSuppressionType");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasFilter("[UserId] IS NOT NULL");

                    b.ToTable("EmailSuppression");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Import", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ImportDashboardStatusType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ImportDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ImportStatusType")
                        .HasColumnType("int");

                    b.Property<int>("ImportTriggerType")
                        .HasColumnType("int");

                    b.Property<int>("ImportType")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("ImportDashboardStatusType");

                    b.HasIndex("ImportStatusType");

                    b.HasIndex("ImportType");

                    b.ToTable("Imports", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportContract", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ContractId")
                        .HasColumnType("int");

                    b.Property<int>("ContractVersionId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ImportContractStatusType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ImportDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ImportId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("NewReferencesCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PubMedCreateDateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PubMedModificationDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ReferencesCount")
                        .HasColumnType("int");

                    b.Property<int>("SilentUpdatesCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("UpdatesCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ImportId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ImportId"), new[] { "ContractId", "PubMedModificationDate", "StartDate", "EndDate", "ImportContractStatusType", "ReferencesCount", "NewReferencesCount", "UpdatesCount", "SilentUpdatesCount" });

                    b.HasIndex("ContractId", "PubMedModificationDate");

                    b.HasIndex("Id", "ImportId");

                    b.HasIndex("ContractVersionId", "ContractId", "ImportId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ContractVersionId", "ContractId", "ImportId"), new[] { "ReferencesCount", "ImportDate" });

                    b.ToTable("ImportContracts", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportContractReferenceClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ICRCType")
                        .HasColumnType("int");

                    b.Property<int>("ImportContractId")
                        .HasColumnType("int");

                    b.Property<int>("ImportId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ReferenceClassificationId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ImportContractId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ImportContractId"), new[] { "ReferenceClassificationId", "ICRCType", "CreatedDate", "CreatedBy", "LastUpdatedDate", "LastUpdatedBy", "ImportId" });

                    b.HasIndex("ImportContractId", "ReferenceClassificationId")
                        .IsUnique();

                    b.HasIndex("ImportId", "ReferenceClassificationId");

                    b.HasIndex("ReferenceClassificationId", "ImportContractId");

                    b.HasIndex("ReferenceClassificationId", "Id", "ImportId");

                    b.ToTable("ImportContractReferenceClassifications", (string)null);

                    b.HasAnnotation("SqlServer:UseSqlOutputClause", false);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportDashboardCountResult", b =>
                {
                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ImportId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TodoCount")
                        .HasColumnType("int");

                    b.ToTable((string)null);

                    b.ToView(null, (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportSelection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ImportId")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ImportId");

                    b.HasIndex("UserId");

                    b.ToTable("ImportSelections", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Journal", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("CronExpression")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Enabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Issn")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<DateTime?>("LastRun")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Url")
                        .HasMaxLength(2048)
                        .HasColumnType("nvarchar(2048)");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.ToTable("Journals", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CompanyId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "Name")
                        .IsUnique();

                    b.ToTable("Projects", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Reference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abstract")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AffiliationTextFirstAuthor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Authors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfOccurrence")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateRevised")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Doi")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FullPagination")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JournalTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("MeshHeadings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("PublicationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PublicationYear")
                        .HasColumnType("int");

                    b.Property<string>("SourceId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SourceSystem")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Volume")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VolumeAbbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SourceId")
                        .IsUnique()
                        .HasFilter("[SourceId] IS NOT NULL");

                    b.HasIndex("Doi", "Id");

                    b.ToTable("References", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ReferencesHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AiCategoryDecision")
                        .HasColumnType("int");

                    b.Property<string>("AiCategoryReason")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<int>("AiDosageFormDecision")
                        .HasColumnType("int");

                    b.Property<string>("AiDosageFormReason")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("AiSuggestedCategory")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("AiSuggestedDosageForm")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ClassificationCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("ClassifierId")
                        .HasColumnType("int");

                    b.Property<string>("CountryOfOccurrence")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("DosageForm")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("MasterAssessorId")
                        .HasColumnType("int");

                    b.Property<string>("MinimalCriteria")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasDefaultValue("");

                    b.Property<int>("PSURRelevanceAbstract")
                        .HasColumnType("int");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int?>("PreAssessorId")
                        .HasColumnType("int");

                    b.Property<string>("PvSafetyDatabaseId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReasonForChange")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("ReferenceId")
                        .HasColumnType("int");

                    b.Property<int>("ReferenceState")
                        .HasColumnType("int");

                    b.Property<int>("SubstanceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ClassificationCategoryId");

                    b.HasIndex("ClassifierId");

                    b.HasIndex("CreatedDate");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CreatedDate"), new[] { "ReferenceId", "SubstanceId", "ClassificationCategoryId" });

                    b.HasIndex("LastUpdatedDate");

                    b.HasIndex("LastUpdatedDate", "PSURRelevanceAbstract");

                    b.HasIndex("ReferenceId", "SubstanceId")
                        .IsUnique();

                    b.HasIndex("Id", "ReferenceState", "PreAssessorId");

                    b.HasIndex("Id", "ReferenceId", "SubstanceId", "ReferenceState");

                    b.HasIndex("LastUpdatedDate", "Id", "ClassificationCategoryId", "ReferenceId");

                    b.HasIndex("ReferenceId", "SubstanceId", "Id", "ClassifierId");

                    b.HasIndex("SubstanceId", "Id", "ReferenceId", "LastUpdatedDate");

                    b.HasIndex("ReferenceState", "Id", "ClassificationCategoryId", "SubstanceId", "ReferenceId", "ClassifierId");

                    b.HasIndex("ReferenceState", "PreAssessorId", "Id", "ClassificationCategoryId", "SubstanceId", "ReferenceId", "ClassifierId");

                    b.ToTable("ReferenceClassifications", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("ReferenceClassificationsHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceClassificationLock", b =>
                {
                    b.Property<int>("ReferenceClassificationId")
                        .HasColumnType("int");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("ReferenceClassificationId", "UserId");

                    b.HasIndex("ReferenceClassificationId")
                        .IsUnique();

                    b.HasIndex("UserId");

                    b.ToTable("ReferenceClassificationLocks", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceHistoryAction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ReferenceClassificationId")
                        .HasColumnType("int");

                    b.Property<int>("ReferenceHistoryActionType")
                        .HasColumnType("int");

                    b.Property<DateTime>("TimeStamp")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ReferenceClassificationId");

                    b.HasIndex("UserId");

                    b.ToTable("ReferenceHistoryActions", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceUpdate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abstract")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AffiliationTextFirstAuthor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Authors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfOccurrence")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateRevised")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentLocation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Doi")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FullPagination")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ImportContractId")
                        .HasColumnType("int");

                    b.Property<string>("Issn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JournalTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("MeshHeadings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PublicationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PublicationYear")
                        .HasColumnType("int");

                    b.Property<int>("ReferenceId")
                        .HasColumnType("int");

                    b.Property<string>("SourceId")
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SourceSystem")
                        .HasColumnType("int");

                    b.Property<int>("SubstanceId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Volume")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VolumeAbbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ReferenceId", "SubstanceId")
                        .IsUnique();

                    b.ToTable("ReferenceUpdates", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Report", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AllCompanies")
                        .HasColumnType("bit");

                    b.Property<string>("Claims")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("ControllerName")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("ControllerName");

                    b.ToTable("Reports", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Substance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "Type")
                        .IsUnique()
                        .HasFilter("[Type] IS NOT NULL");

                    b.ToTable("Substances", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("SubstancesHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.SubstanceSynonym", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SubstanceId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SubstanceId", "Name");

                    b.ToTable("SubstanceSynonyms", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.UserEmailPreference", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("EmailPreferenceId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("UserId", "EmailPreferenceId");

                    b.HasIndex("EmailPreferenceId");

                    b.ToTable("UserEmailPreferences", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.UserSubstance", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("SubstanceId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("UserId", "SubstanceId");

                    b.HasIndex("SubstanceId");

                    b.HasIndex("UserId", "SubstanceId");

                    b.ToTable("UserSubstances", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.UserManagement.Claim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.ToTable("Claims", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.UserManagement.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ActivationExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("FamilyName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("GivenName")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<string>("InvitationEmailLink")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("PeriodEnd")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodEnd");

                    b.Property<DateTime>("PeriodStart")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasColumnName("PeriodStart");

                    b.Property<int>("QCPercentage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(10);

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasDatabaseName("UX_Users_Email");

                    b.ToTable("Users", (string)null);

                    b.ToTable(tb => tb.IsTemporal(ttb =>
                            {
                                ttb.UseHistoryTable("UsersHistory");
                                ttb
                                    .HasPeriodStart("PeriodStart")
                                    .HasColumnName("PeriodStart");
                                ttb
                                    .HasPeriodEnd("PeriodEnd")
                                    .HasColumnName("PeriodEnd");
                            }));
                });

            modelBuilder.Entity("PharmaLex.VigiLit.ImportManagement.Entities.FailedImportFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abstract")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("AbstractConfidence")
                        .HasColumnType("tinyint");

                    b.Property<bool>("AbstractConfidenceCheckPassed")
                        .HasColumnType("bit");

                    b.Property<string>("AffiliationTextFirstAuthor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("AffiliationTextFirstAuthorConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Authors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("AuthorsConfidence")
                        .HasColumnType("tinyint");

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CountryOfOccurrence")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("CountryOfOccurrenceConfidence")
                        .HasColumnType("tinyint");

                    b.Property<bool>("CountryOfOccurrenceConfidenceCheckPassed")
                        .HasColumnType("bit");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateRevised")
                        .HasColumnType("datetime2");

                    b.Property<byte>("DateRevisedConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("DocumentLocation")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("Doi")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<byte>("DoiConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullPagination")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("FullPaginationConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Issn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("IssnConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Issue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("IssueConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("JournalTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("JournalTitleConfidence")
                        .HasColumnType("tinyint");

                    b.Property<bool>("JournalTitleConfidenceCheckPassed")
                        .HasColumnType("bit");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("KeywordsConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("LanguageConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("MeshHeadings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("MeshHeadingsConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("PublicationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("PublicationTypeConfidence")
                        .HasColumnType("tinyint");

                    b.Property<int?>("PublicationYear")
                        .HasColumnType("int");

                    b.Property<byte>("PublicationYearConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("SourceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("SourceIdConfidence")
                        .HasColumnType("tinyint");

                    b.Property<int>("SourceSystem")
                        .HasColumnType("int");

                    b.Property<byte>("SourceSystemConfidence")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("TitleConfidence")
                        .HasColumnType("tinyint");

                    b.Property<bool>("TitleConfidenceCheckPassed")
                        .HasColumnType("bit");

                    b.Property<string>("Volume")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VolumeAbbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("VolumeAbbreviationConfidence")
                        .HasColumnType("tinyint");

                    b.Property<byte>("VolumeConfidence")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("FailedImportFiles", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.ImportManagement.Entities.ImportFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<Guid>("BatchId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("FileHash")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FileSize")
                        .HasColumnType("int");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("FileHash")
                        .IsUnique();

                    b.ToTable("ImportFiles", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.ImportManagement.Entities.ImportManualEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abstract")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AffiliationTextFirstAuthor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Authors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfOccurrence")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateRevised")
                        .HasColumnType("datetime2");

                    b.Property<string>("Doi")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FullPagination")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JournalTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("MeshHeadings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PublicationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PublicationYear")
                        .HasColumnType("int");

                    b.Property<string>("SourceId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SourceSystem")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Volume")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VolumeAbbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ImportManualEntry", (string)null);
                });

            modelBuilder.Entity("PharmaLex.VigiLit.ImportManagement.Entities.ImportReference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abstract")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AffiliationTextFirstAuthor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Authors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfOccurrence")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("DateRevised")
                        .HasColumnType("datetime2");

                    b.Property<string>("Doi")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FullPagination")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Issue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JournalTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastUpdatedBy")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime>("LastUpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("MeshHeadings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PublicationType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PublicationYear")
                        .HasColumnType("int");

                    b.Property<string>("SourceId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SourceSystem")
                        .HasColumnType("int");

                    b.Property<int>("StatusType")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Volume")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VolumeAbbreviation")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ImportReferences", (string)null);
                });

            modelBuilder.Entity("ClaimUser", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.Claim", null)
                        .WithMany()
                        .HasForeignKey("ClaimsInternalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", null)
                        .WithMany()
                        .HasForeignKey("UsersInternalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("PharmaLex.Core.UserSessionManagement.Entities.UserSession", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.AdHocImportContract", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.AdHocImport", "AdHocImport")
                        .WithMany("AdHocImportContracts")
                        .HasForeignKey("AdHocImportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Contract", "Contract")
                        .WithMany("AdHocImportContracts")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdHocImport");

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CaseCompanies", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Cases", "Case")
                        .WithMany("CaseCompanies")
                        .HasForeignKey("CaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Case");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CaseFiles", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Cases", "Case")
                        .WithMany("CaseFiles")
                        .HasForeignKey("CaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Case");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Cases", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", "ReferenceClassification")
                        .WithMany()
                        .HasForeignKey("ReferenceClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReferenceClassification");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyInterest", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", "ReferenceClassification")
                        .WithMany()
                        .HasForeignKey("ReferenceClassificationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Reference", "Reference")
                        .WithMany()
                        .HasForeignKey("ReferenceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Reference");

                    b.Navigation("ReferenceClassification");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyReport", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany("CompanyReports")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Report", "Report")
                        .WithMany("CompanyReports")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyUser", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany("CompanyUsers")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithOne("CompanyUser")
                        .HasForeignKey("PharmaLex.VigiLit.Domain.Models.CompanyUser", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Contract", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Project", "Project")
                        .WithMany("ContractsInternal")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Substance", "Substance")
                        .WithMany()
                        .HasForeignKey("SubstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("Substance");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ContractVersion", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Contract", "Contract")
                        .WithMany("ContractVersionsInternal")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany("ContractVersions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ContractVersionJournal", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ContractVersion", "ContractVersion")
                        .WithMany("ContractVersionJournals")
                        .HasForeignKey("ContractVersionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Journal", "Journal")
                        .WithMany("ContractVersionJournals")
                        .HasForeignKey("JournalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContractVersion");

                    b.Navigation("Journal");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailMessage", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Email", "Email")
                        .WithMany("EmailMessages")
                        .HasForeignKey("EmailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Email");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailMessageRelevantEvent", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.EmailMessage", "EmailMessage")
                        .WithMany("EmailMessageRelevantEvents")
                        .HasForeignKey("EmailMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.EmailRelevantEvent", "EmailRelevantEvent")
                        .WithMany()
                        .HasForeignKey("EmailRelevantEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EmailMessage");

                    b.Navigation("EmailRelevantEvent");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailRelevantEvent", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ClassificationCategory", "ClassificationCategory")
                        .WithMany()
                        .HasForeignKey("ClassificationCategoryId");

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.CompanyInterest", "CompanyInterest")
                        .WithMany("EmailRelevantEvents")
                        .HasForeignKey("CompanyInterestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ClassificationCategory");

                    b.Navigation("CompanyInterest");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailSuppression", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithOne("EmailSuppression")
                        .HasForeignKey("PharmaLex.VigiLit.Domain.Models.EmailSuppression", "UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportContract", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Contract", "Contract")
                        .WithMany("ImportContracts")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Import", "Import")
                        .WithMany("ImportContracts")
                        .HasForeignKey("ImportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Import");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportContractReferenceClassification", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ImportContract", "ImportContract")
                        .WithMany("ImportContractReferenceClassifications")
                        .HasForeignKey("ImportContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Import", "Import")
                        .WithMany()
                        .HasForeignKey("ImportId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", "ReferenceClassification")
                        .WithMany("ImportContractReferenceClassifications")
                        .HasForeignKey("ReferenceClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Import");

                    b.Navigation("ImportContract");

                    b.Navigation("ReferenceClassification");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportSelection", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Import", "Import")
                        .WithMany()
                        .HasForeignKey("ImportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Import");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Journal", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Country", "Country")
                        .WithMany("Journals")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Project", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Company", "Company")
                        .WithMany("Projects")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ClassificationCategory", "ClassificationCategory")
                        .WithMany()
                        .HasForeignKey("ClassificationCategoryId");

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "Classifier")
                        .WithMany()
                        .HasForeignKey("ClassifierId");

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Reference", "Reference")
                        .WithMany("ReferenceClassificationsInternal")
                        .HasForeignKey("ReferenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Substance", "Substance")
                        .WithMany()
                        .HasForeignKey("SubstanceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ClassificationCategory");

                    b.Navigation("Classifier");

                    b.Navigation("Reference");

                    b.Navigation("Substance");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceClassificationLock", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", "ReferenceClassification")
                        .WithMany()
                        .HasForeignKey("ReferenceClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany("ReferenceClassificationLocks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ReferenceClassification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceHistoryAction", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", "ReferenceClassification")
                        .WithMany("ReferenceHistoryActions")
                        .HasForeignKey("ReferenceClassificationId");

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("ReferenceClassification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.SubstanceSynonym", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Substance", "Substance")
                        .WithMany("SubstanceSynonyms")
                        .HasForeignKey("SubstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Substance");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.UserEmailPreference", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.EmailPreference", "EmailPreference")
                        .WithMany("UserEmailPreferences")
                        .HasForeignKey("EmailPreferenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany("UserEmailPreferences")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EmailPreference");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.UserSubstance", b =>
                {
                    b.HasOne("PharmaLex.VigiLit.Domain.Models.Substance", "Substance")
                        .WithMany("UserSubstances")
                        .HasForeignKey("SubstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("PharmaLex.VigiLit.Domain.UserManagement.User", "User")
                        .WithMany("UserSubstances")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Substance");

                    b.Navigation("User");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.AdHocImport", b =>
                {
                    b.Navigation("AdHocImportContracts");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Cases", b =>
                {
                    b.Navigation("CaseCompanies");

                    b.Navigation("CaseFiles");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Company", b =>
                {
                    b.Navigation("CompanyReports");

                    b.Navigation("CompanyUsers");

                    b.Navigation("Projects");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.CompanyInterest", b =>
                {
                    b.Navigation("EmailRelevantEvents");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Contract", b =>
                {
                    b.Navigation("AdHocImportContracts");

                    b.Navigation("ContractVersionsInternal");

                    b.Navigation("ImportContracts");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ContractVersion", b =>
                {
                    b.Navigation("ContractVersionJournals");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Country", b =>
                {
                    b.Navigation("Journals");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Email", b =>
                {
                    b.Navigation("EmailMessages");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailMessage", b =>
                {
                    b.Navigation("EmailMessageRelevantEvents");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.EmailPreference", b =>
                {
                    b.Navigation("UserEmailPreferences");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Import", b =>
                {
                    b.Navigation("ImportContracts");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ImportContract", b =>
                {
                    b.Navigation("ImportContractReferenceClassifications");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Journal", b =>
                {
                    b.Navigation("ContractVersionJournals");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Project", b =>
                {
                    b.Navigation("ContractsInternal");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Reference", b =>
                {
                    b.Navigation("ReferenceClassificationsInternal");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.ReferenceClassification", b =>
                {
                    b.Navigation("ImportContractReferenceClassifications");

                    b.Navigation("ReferenceHistoryActions");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Report", b =>
                {
                    b.Navigation("CompanyReports");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.Models.Substance", b =>
                {
                    b.Navigation("SubstanceSynonyms");

                    b.Navigation("UserSubstances");
                });

            modelBuilder.Entity("PharmaLex.VigiLit.Domain.UserManagement.User", b =>
                {
                    b.Navigation("CompanyUser");

                    b.Navigation("ContractVersions");

                    b.Navigation("EmailSuppression");

                    b.Navigation("ReferenceClassificationLocks");

                    b.Navigation("UserEmailPreferences");

                    b.Navigation("UserSubstances");
                });
#pragma warning restore 612, 618
        }
    }
}
