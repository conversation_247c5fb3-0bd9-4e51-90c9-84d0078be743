﻿using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;
using Task = System.Threading.Tasks.Task;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests;

public class ApifyNotificationServiceTest
{
    private readonly IApifyNotification _notificationService;
    private readonly Mock<ILogger<ApifyNotification>> _mockLogger = new();
    private readonly Mock<IApifyClient> _apifyClient = new();
    private readonly Mock<IDownloadBlobStorage> _blobStorage = new();
    private readonly Mock<IDataExtractionClient> _client = new();

    public ApifyNotificationServiceTest()
    {
        _notificationService = new ApifyNotification(_apifyClient.Object, _blobStorage.Object, _client.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task RunSucceedMethod_ValidPayload()
    {
        var actorTaskId = Fake.GetRandomString(5);
        var mockFilePaths = new List<string>
        {
            "scraping/folder1/file1.pdf",
            "scraping/folder1/file2.txt"
        };
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = Fake.GetRandomString(5),
                defaultKeyValueStoreId = Fake.GetRandomString(5)
            }
        };

        _blobStorage.Setup(x => x.GetBlobPathsAsync(
           actorTaskId,
           It.IsAny<CancellationToken>()
       ))
       .ReturnsAsync(mockFilePaths);

        _client.Setup(x => x.Send(It.IsAny<ExtractDataCommand>()))
          .Returns(Task.CompletedTask);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Once());
        _apifyClient.Verify(x => x.TransferFilesAsync(payload.resource.defaultDatasetId, payload.resource.defaultKeyValueStoreId, _blobStorage.Object, default), Times.Once());

        /// Basic verification that Send was called twice
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                     Times.Exactly(2));

        // More detailed verification
        foreach (var filePath in mockFilePaths)
        {
            _client.Verify(x => x.Send(It.Is<ExtractDataCommand>(cmd =>
                cmd.BatchId == actorTaskId &&
                cmd.FileName == filePath &&
                cmd.Source == Source.File
            )), Times.Once());
        }
    }

    [Theory]
    [InlineData("", "XYZ")]
    [InlineData("XYZ", "")]
    [InlineData("", "")]
    [InlineData(null, null)]
    public async Task RunSucceedMethod_InvalidPayload_DoesNotCallTransferFiles(string defaultDatasetId, string defaultKeyValueStoreId)
    {
        var actorTaskId = Fake.GetRandomString(5);
        var payload = new ApifyWebhookPayload()
        {
            createdAt = DateTime.UtcNow,
            resource = new Resource()
            {
                actorTaskId = actorTaskId,
                defaultDatasetId = defaultDatasetId,
                defaultKeyValueStoreId = defaultKeyValueStoreId
            }
        };

        await _notificationService.RunSucceeded(payload);

        _blobStorage.Verify(x => x.SetBlobFolderName(actorTaskId), Times.Never);
        _apifyClient.Verify(x => x.TransferFilesAsync(payload.resource.defaultDatasetId, payload.resource.defaultKeyValueStoreId, _blobStorage.Object, default), Times.Never);
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()),
                    Times.Never);
    }

    [Fact]
    public async Task RunSucceedMethod_EmptyFolder_DoesNotCallSend()
    {
        // Arrange
        var payload = new ApifyWebhookPayload { resource = new Resource() };
        _blobStorage.Setup(x => x.GetBlobPathsAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync([]);

        // Act
        await _notificationService.RunSucceeded(payload);

        // Assert
        _client.Verify(x => x.Send(It.IsAny<ExtractDataCommand>()), Times.Never());
    }
}
