﻿using AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;

namespace PharmaLex.VigiLit.Application.AutoMapper;
public class FailedImportMappingProfile : Profile
{

    public FailedImportMappingProfile()
    {
        CreateMap<FailedImportFile, FailedImportFileModel>().ReverseMap();
        CreateMap<FailedImportFile, ImportReference>()
            .ForMember(x => x.Id, opt => opt.Ignore());
    }
}

