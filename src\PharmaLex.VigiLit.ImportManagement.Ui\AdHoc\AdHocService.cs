﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using System.Globalization;

namespace PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;
internal class AdHocService : IAdHocService
{
    private readonly IAdHocImportRepository _adHocImportRepository;

    public AdHocService(
        IAdHocImportRepository adHocImportRepository)
    {
        _adHocImportRepository = adHocImportRepository;
    }

    public async Task<List<ImportDisplayCard>> GetCards()
    {
        var pubMedAdhocImports = await GetAdHocImportsList();

        var displayCards = new List<ImportDisplayCard>();

        foreach (var import in pubMedAdhocImports)
        {
            displayCards.Add(new ImportDisplayCard()
            {
                Id = import.Id.ToString(),
                ImportType = "Pubmed",
                Title = "",
                Filename = "",
                NumberOfContracts = import.ContractsCount,
                DateFrom = import.SourceSearchStartDate,
                DateTo = import.SourceSearchEndDate,
                FileCount = 0,
                FilesPlus = 0,
                CreatedBy = "",
                LastUpdatedBy = ""
            });
        }
        return displayCards;
    }

    public async Task Add(AdHocImportModel model)
    {
        var dates = ValidateToDateIsNotPriorFromDate(model);
        var adHocImport = new AdHocImport(dates.fromDate, dates.toDate);

        foreach (var contractId in model.SelectedContractIds)
        {
            adHocImport.AdHocImportContracts.Add(new AdHocImportContract(contractId));
        }

        _adHocImportRepository.Add(adHocImport);
        await _adHocImportRepository.SaveChangesAsync();
    }

    public async Task Abandon(int id)
    {
        var adHocImport = await _adHocImportRepository.GetById(id);

        adHocImport.AdHocImportStatusType = AdHocImportStatusType.Abandoned;

        await _adHocImportRepository.SaveChangesAsync();
    }

    public async Task<IEnumerable<AdHocImportModel>> GetAdHocImportsList()
    {
        return await _adHocImportRepository.GetForList();
    }

    private static (DateTime fromDate, DateTime toDate) ValidateToDateIsNotPriorFromDate(AdHocImportModel model)
    {
        var sourceSearchStartDate = DateTime.Parse(model.SourceSearchStartDate, CultureInfo.InvariantCulture);
        var sourceSearchEndDate = DateTime.Parse(model.SourceSearchEndDate, CultureInfo.InvariantCulture);

        if (sourceSearchEndDate < sourceSearchStartDate)
        {
            throw new ArgumentException("To date cannot be prior from date");
        }

        return (sourceSearchStartDate, sourceSearchEndDate);
    }
}
