﻿using PharmaLex.VigiLit.DataExtraction.Service.Data;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

public interface ICallbackHandler
{
    /// <summary>
    /// Gets the document stream.
    /// </summary>
    /// <returns></returns>
    Task<Stream> GetDocumentStream(Guid correlationId);

    /// <summary>
    /// Handles a success callback by processing the extracted metadata and updating the corresponding queue item's status.
    /// </summary>
    /// <param name="correlationId">The correlation ID used to retrieve the corresponding queue item.</param>
    /// <param name="extractedMetaData">The extracted metadata to be processed.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    /// <exception cref="ArgumentException">Thrown when no queue item is found for the given correlation ID.</exception>
    Task Success(Guid correlationId, ExtractedMetadata extractedMetaData);

    /// <summary>
    /// Handles the case where PhlexVision has returned error.
    /// </summary>
    Task Error(Guid correlationId);


}