﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.Logging;
using System.Text.Json;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

[Route("api/[controller]")]
[ApiController]
[AllowAnonymous]
public class PhlexVisionApiController(
                            ICallbackHandler callbackHandler,
                            ILogger<PhlexVisionApiController> logger) : ControllerBase
{
    [HttpGet("DownloadDocument/{documentId}/{filename}/")]
    public async Task<IActionResult> DownloadDocument([FromRoute] DownloadDocumentsCommandRequest downloadRequest,
        string documentId, string filename)
    {
        logger.LogInformation("PhlexVisionApiController: DownloadDocument: {CorrelationId} : {DocumentId} : {FileName}",
            LogSanitizer.Sanitize(downloadRequest.CorrelationId ?? "Null CorrelationId"),
            LogSanitizer.Sanitize(downloadRequest.DocumentId ?? "Null DocumentId"),
            LogSanitizer.Sanitize(downloadRequest.FileName ?? "Null FileName"));

        if (!TryParse(downloadRequest, out var correlationId))
        {
            throw new ArgumentException($"Correlation Id is empty for {downloadRequest.FileName}");
        }


        return new FileStreamResult(await callbackHandler.GetDocumentStream(correlationId), "application/pdf")
        {
            FileDownloadName = filename,
        };
    }

    [HttpPost("success/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Success([FromRoute][FromBody] SuccessCallbackRequest request, string documentId)
    {
        logger.LogInformation("PhlexVisionApiController: Success: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));


        if (!TryParse(request, out var correlationId))
        {
            return BadRequest();
        }
        var extractedMetaData = await GetExtractedMetaData(HttpContext.Request.Body);
        await callbackHandler.Success(correlationId, extractedMetaData);

        return Ok();
    }

    [HttpPost("error/{documentId}/")]
    [IgnoreAntiforgeryToken]
    public async Task<IActionResult> Error([FromRoute][FromBody] ErrorCallbackRequest request, string documentId)
    {
        logger.LogInformation("PhlexVisionApiController: Error: {CorrelationId}",
            LogSanitizer.Sanitize(request.CorrelationId ?? "Null CorrelationId"));

        if (!TryParse(request, out var correlationId))
        {
            return BadRequest();
        }

        await callbackHandler.Error(correlationId);

        return Ok();
    }

    private static bool TryParse(IHasCorrelationId request, out Guid correlationId)
    {
        return Guid.TryParse(request.CorrelationId, out correlationId);
    }

    private readonly JsonSerializerOptions _caseInsensitiveSerializerOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };

    private async Task<ExtractedMetadata> GetExtractedMetaData(Stream responseBodyStream)
    {
        using var reader = new StreamReader(responseBodyStream);
        var body = await reader.ReadToEndAsync();
        logger.LogInformation("{Body}", LogSanitizer.Sanitize(body));

        var root = JsonSerializer.Deserialize<Rootobject>(body, _caseInsensitiveSerializerOptions);

        if (root == null) throw new ArgumentException("Could not deserialize response body.");

        var extractedMetaData = root.ExtractedMetadata;

        if (extractedMetaData == null) throw new ArgumentException("Could not deserialize extracted meta data.");

        return extractedMetaData;
    }
}
