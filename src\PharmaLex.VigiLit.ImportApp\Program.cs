using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using NLog;
using NLog.Web;
using PharmaLex.Authentication.B2C;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.Core.Configuration;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.DataAccess;
using PharmaLex.FeatureManagement.Extensions;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataExtraction.Service;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportApp.Common;
using PharmaLex.VigiLit.ImportManagement;
using PharmaLex.VigiLit.ImportManagement.Service;
using PharmaLex.VigiLit.ImportManagement.Ui;
using PharmaLex.VigiLit.Infrastructure;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed;
using PharmaLex.VigiLit.ReferenceManagement.Service;
using PharmaLex.VigiLit.Reporting;
using PharmaLex.VigiLit.Reporting.Contracts.AutoMapper;
using Phlex.Core.MessageBus.Extensions;
using System;
using System.Reflection;
using System.Threading.Tasks;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace PharmaLex.VigiLit.ImportApp;

public static class Program
{
    public static async Task Main()
    {
        //Required for logs in context
        var nrAgent = new Lazy<NewRelic.Api.Agent.IAgent>(NewRelic.Api.Agent.NewRelic.GetAgent());
        foreach (var (key, value) in nrAgent.Value.GetLinkingMetadata())
        {
            GlobalDiagnosticsContext.Set(key, value);
        }

        var host = new HostBuilder()
            .ConfigureFunctionsWebApplication()
            .AddConfiguration(Assembly.GetExecutingAssembly())
            .ConfigureServices((hostContext, services) =>
            {
                services.AddDistributedMemoryCache();

                services.AddScoped<IVigiLitUserContext, VigilitUserContext>();
                services.AddScoped<IUserContext, VigilitUserContext>();
                services.AddScoped<IWebsiteUriProvider, WebsiteUriProvider>();
                services.RegisterPubmedImportServices();
                services.RegisterRepositories();
                services.RegisterReporting(hostContext.Configuration);
                services.RegisterReferenceManagement(hostContext.Configuration);
                services.RegisterImportManagement(hostContext.Configuration);
                services.RegisterImportManagementUi(hostContext.Configuration); // Shouldn't be here
                services.RegisterImportManagementClient();
                services.RegisterDataExtractionServices(hostContext.Configuration);

                services.RegisterDataExtractionClient();

                // These two are added to fix exception related to Reports.TrackingSheets.Domain.Service and Ui.FileImport.IImportFileService
                services.AddScoped<IReportingCompanyRepository, CompanyRepository>();
                services.AddImportFileUploadDocumentServices(hostContext.Configuration);
                // These two are added to fix exception related to Reports.TrackingSheets.Domain.Service and Ui.FileImport.IImportFileService

                // These two are related to importing so will need moving

                services.RegisterPubMedAggregator(); // This is the one in "PharmaLex.VigiLit.Aggregators.PubMed", needs combining
                services.AddScoped<IContractService, ContractService>();

                // These two are related to importing so will need moving

                services.AddTransient<IGraphClientProvider<AzureAdB2CGraphOptions>, AzureAdB2CGraphClientProvider>();
                services.AddScoped<IAzureAdB2CGraphService, AzureAdB2CGraphService>();
                services.AddUserServices<User, UserRepository>(hostContext.Configuration);
                services.AddScoped<IOidcService, OidcService>();

                services.AddScoped<IClassificationService, ClassificationService>();
                services.AddScoped<IDistributedCacheService, DistributedCacheService>();
                services.AddScoped<IDistributedCacheServiceFactory, DistributedCacheServiceFactory>();
                services.AddScoped<IClassificationCategoryService, ClassificationCategoryService>();
                services.AddScoped<ICountryService, CountryService>();
                services.AddScoped<IReferenceService, ReferenceService>();
                services.AddScoped<IUserService, UserService>();
                services.AddScoped<ICompanyService, CompanyService>();
                services.AddScoped<ICompanyUserService, CompanyUserService>();
                services.AddScoped<ICaseService, CaseService>();
                services.AddScoped<ICaseFileDocumentTypeService, CaseFileDocumentTypeService>();
                services.AddScoped<IUserSessionService, UserSessionService>();
                services.AddScoped<ISubstanceService, SubstanceService>();

                services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

                services.AddAutoMapper(typeof(UserMappingProfile).Assembly);
                services.AddAutoMapper(typeof(ReportMappingProfile).Assembly);

                services.AddEmailServices(hostContext.Configuration);
                services.AddDocumentServices(hostContext.Configuration);

                services
                    .Configure<ImportOptions>(hostContext.Configuration.GetSection(ImportOptions.ImportSettings))
                    .Configure<AzureAdGraphOptions>(hostContext.Configuration.GetSection("AzureAdGraph"))
                    .Configure<AzureAdB2CGraphOptions>(hostContext.Configuration.GetSection("AzureAdB2CGraph"));

                JsonConvert.DefaultSettings = () => new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                var configuration = hostContext.Configuration;
                services.AddMessageBus(configuration, configurator =>
                {
                    configurator.UsingTransportFromConfiguration(configuration);
                });

                services.RegisterAiAnalysisClient();

                services.AddPharmaLexFeatureManagement();
            })
            .ConfigureLogging((hostingContext, logging) =>
            {
                logging.ClearProviders();
                logging.SetMinimumLevel(LogLevel.Trace);
            }).UseNLog()
            .Build();

        await host.RunAsync();
    }
}