﻿using PharmaLex.VigiLit.DataExtraction.Service.Data;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

/// <summary>
/// Checks whether the extracted reference passes quality control.
/// </summary>
/// <remarks>
/// To pass Quality Control all the following must be true:
/// - All mandatory properties must have a value
/// - All mandatory properties must have a confidence level greater than or equal to the configured value
/// - All non-mandatory properties must meet the minimum confidence level for non-mandatory
/// - The overall weighted confidence must meet the weighted level
/// </remarks>
internal interface IQualityControlChecker
{
    /// <summary>
    /// Checks whether the extracted reference passes quality control.
    /// </summary>
    /// <param name="reference">The reference.</param>
    /// <returns></returns>
    bool DoesPassQualityControl(ExtractedReference reference);
}
