﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;
using PharmaLex.VigiLit.Application.Services.Comparers;
using PharmaLex.VigiLit.Application.Services.Helpers;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.Application.Services;

#pragma warning disable S6677

public class PreClassificationService : IPreClassificationService
{
    private readonly ILogger<PreClassificationService> _logger;
    private readonly IMapper _mapper;
    private readonly IVigiLitUserContext _userContext;

    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IReferenceUpdateRepository _referenceUpdateRepository;
    private readonly IReferenceHistoryActionRepository _referenceHistoryActionRepository;
    private readonly IReferenceClassificationLockRepository _referenceClassificationLockRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly IAiSuggestionRepository _aiSuggestionRepository;
    private readonly ISubstanceService _substanceService;

    private readonly IDashboardService _dashboardService;

    private readonly IPreClassifyModelHelper _preClassifyModelHelper;

    private readonly IAiReferencePublisher _aiReferencePublisher;
    private readonly IFeatureManager _featureManager;
    private const string DisplayAiSuggestions = "DisplayAiSuggestions";

    [SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters",
        Justification = "This service will as a by-product reduce the classification dependencies where the code came and this number will reduce as part of that work")]
    public PreClassificationService(
        ILoggerFactory loggerFactory,
        IMapper mapper,
        IVigiLitUserContext userContext,

        IReferenceClassificationLockRepository referenceClassificationLockRepository,
        IReferenceClassificationRepository referenceClassificationRepository,
        IReferenceUpdateRepository referenceUpdateRepository,
        IReferenceHistoryActionRepository referenceHistoryActionRepository,
        IReferenceRepository referenceRepository,
        IDashboardService dashboardService,
        IPreClassifyModelHelper preClassifyModelHelper,
        IFeatureManager featureManager,
        IAiReferencePublisher aiReferencePublisher,
        ISubstanceService substanceService,
        IAiSuggestionRepository aiSuggestionRepository)
    {
        _logger = loggerFactory.CreateLogger<PreClassificationService>();
        _mapper = mapper;
        _userContext = userContext;

        _referenceClassificationLockRepository = referenceClassificationLockRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _referenceUpdateRepository = referenceUpdateRepository;
        _referenceHistoryActionRepository = referenceHistoryActionRepository;
        _referenceRepository = referenceRepository;
        _aiSuggestionRepository = aiSuggestionRepository;

        _dashboardService = dashboardService;

        _preClassifyModelHelper = preClassifyModelHelper;

        _featureManager = featureManager;
        _aiReferencePublisher = aiReferencePublisher;
        _substanceService = substanceService;
    }

    public async Task PreClassifyAsync(IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels)
    {
        if (preClassifyReferenceModels.Any())
        {
            foreach (var updatedClassification in preClassifyReferenceModels)
            {
                await UpdateClassification(updatedClassification);
            }

            await _referenceClassificationRepository.SaveChangesAsync(); // NOTE: Unnecessary - needs UoW really
            await _referenceUpdateRepository.SaveChangesAsync();
            await _referenceHistoryActionRepository.SaveChangesAsync();
        }

        var unlockingResult = await _referenceClassificationLockRepository.Unlock(_userContext.UserId);

        var updatedClassificationIds = preClassifyReferenceModels.Any() ? string.Join(",", preClassifyReferenceModels.Select(x => x.ReferenceClassification.Id)) : "none";
        var unlockedClassificationIds = unlockingResult.ClassificationIds.Any() ? string.Join(",", unlockingResult.ClassificationIds) : "none";

        _logger.LogInformation("Preclassify: UserId={userId} preclassified updatedClassificationIds={updatedClassificationIds}. " +
                               "Locks released unlockedClassificationIds={unlockedClassificationIds}",
            _userContext.UserId, updatedClassificationIds, unlockedClassificationIds);
    }

    private async Task UpdateClassification(PreclassifyReferenceModel updatedClassification)
    {
        var classification = await _referenceClassificationRepository.GetByIdAsync(updatedClassification.ReferenceClassification.Id);

        if (classification == null)
        {
            throw new ArgumentException($"classification could not be found");
        }
        if (IsUpdatedClassificationNew(updatedClassification))
        {
            await PreClassifyNewClassification(classification, updatedClassification);
        }
        else
        {
            await PreClassifyUpdatedClassification(classification, updatedClassification);
        }
    }

    private static bool IsUpdatedClassificationNew(PreclassifyReferenceModel updatedClassification)
    {
        return updatedClassification.ReferenceUpdate == null;
    }

    private async Task PreClassifyNewClassification(ReferenceClassification classification, PreclassifyReferenceModel updatedClassification)
    {
        if (updatedClassification.ReferenceClassification.IsActive)
        {
            await PreClassifyClassification(classification, updatedClassification);
        }
        else
        {
            DeactivateClassification(classification);
        }
    }

    private async Task PreClassifyUpdatedClassification(ReferenceClassification classification, PreclassifyReferenceModel updatedClassification)
    {
        if (updatedClassification.ReferenceUpdate.Id > 0)
        {
            await ResolveUpdatesTable(classification, updatedClassification);
        }

        if (updatedClassification.ReferenceClassification.IsActive)
        {
            await ResolveChangedClassification(classification, updatedClassification);
        }
        else
        {
            DeactivateClassification(classification);
        }
    }

    /// <summary>
    /// Resolves the changed classification.
    /// </summary>
    /// <param name="classification">The classification.</param>
    /// <param name="updatedClassification">The updated classification.</param>
    /// <remarks>
    /// Only pre-classify a changed classification. If the dosage form has been changed then that is not considered as a change.
    /// </remarks>
    private async Task ResolveChangedClassification(ReferenceClassification classification, PreclassifyReferenceModel updatedClassification)
    {
        if (HasClassificationCategoryChanged(updatedClassification.ReferenceClassification, classification))
        {
            await PreClassifyClassification(classification, updatedClassification);
            return;
        }

        if (HasClassificationDosageFormChanged(updatedClassification.ReferenceClassification, classification))
        {
            classification.UpdateDosageForm(updatedClassification.ReferenceClassification.DosageForm,
                _userContext.UserId);
        }

        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Unchanged);
    }

    private async Task ResolveUpdatesTable(ReferenceClassification classification, PreclassifyReferenceModel updatedClassification)
    {
        ReferenceUpdate update = await _referenceUpdateRepository.GetById(updatedClassification.ReferenceUpdate.Id);

        if (update == null)
        {
            _logger.LogWarning(
                "PreClassifyAsync could not TakeUpdates because update is null. classificationId={classificationId}, updateId={updateId}",
                updatedClassification.ReferenceClassification.Id, updatedClassification.ReferenceUpdate.Id);
            return;
        }

        classification.Reference.TakeUpdates(update);
        _referenceUpdateRepository.Remove(update);
    }

    private async Task PreClassifyClassification(ReferenceClassification classification, PreclassifyReferenceModel updatedClassification)
    {
        classification.PreClassify(
            updatedClassification.ReferenceClassification.ClassificationCategoryId,
            updatedClassification.ReferenceClassification.DosageForm,
            updatedClassification.ReferenceClassification.CountryOfOccurrence,
            _userContext.UserId);

        if (updatedClassification.AiSuggestedClassification != null && await _featureManager.IsEnabledAsync(DisplayAiSuggestions))
        {
            classification.SetAiCategoryFields(
                (AiCategoryDecision)updatedClassification.AiSuggestedClassification.CategoryDecision,
                updatedClassification.AiSuggestedClassification.Category,
                updatedClassification.AiSuggestedClassification.CategoryReason);

            classification.SetAiDosageFormFields(
                (AiDosageFormDecision)updatedClassification.AiSuggestedClassification.DosageFormDecision,
                updatedClassification.AiSuggestedClassification.DosageForm,
                updatedClassification.AiSuggestedClassification.DosageFormReason);
        }

        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Classified);
    }

    private void DeactivateClassification(ReferenceClassification classification)
    {
        classification.Deactivate();
        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Inactive);
    }

    private void AddReferenceHistoryAction(int classificationId, ReferenceHistoryActionType historyActionType)
    {
        var historyAction = new ReferenceHistoryAction(classificationId, historyActionType, _userContext.UserId);
        _referenceHistoryActionRepository.Add(historyAction);
    }

    private static bool HasClassificationCategoryChanged(ReferenceClassificationModel a, ReferenceClassification b)
    {
        return a.ClassificationCategoryId != b.ClassificationCategoryId;
    }

    private static bool HasClassificationDosageFormChanged(ReferenceClassificationModel a, ReferenceClassification b)
    {
        return a.DosageForm != b.DosageForm;
    }

    public async Task<IEnumerable<PreclassifyReferenceModel>?> PickLockedClassifications()
    {
        // User goes to the pre-classification page, if they have locks held because they took some locks and 
        // then navigated away and came back then they expect those classifications to load.
        var locksForUser = await _referenceClassificationLockRepository.GetLocksForUser(_userContext.UserId);

        if (locksForUser.Any())
        {
            var lockedClassificationIds = string.Join(",", locksForUser.Select(x => x.ReferenceClassificationId));
            _logger.LogInformation("PickLockedClassifications: User {UserId} loading held locks. classificationIds={ClassificationIds}", _userContext.UserId, lockedClassificationIds);

            var classifications = await _referenceClassificationRepository.GetReferenceClassificationsToPreClassify_Locked(locksForUser.Select(x => x.ReferenceClassificationId).ToList());

            var preClassifyReferenceModels = await BuildPreClassifyReferenceModels(classifications);
            preClassifyReferenceModels.Sort(new PreclassifyReferenceModelComparer());
            return preClassifyReferenceModels;
        }

        return null;
    }

    public async Task RetryAiSuggestions(ReferenceClassificationModel referenceModel)
    {
        _logger.LogInformation("Retrying AI for Substance ID: {SubstanceId} and  Reference Id: {Id}", referenceModel.SubstanceId, referenceModel.ReferenceId);
        var substanceDetails = await _substanceService.GetByIdAsync(referenceModel.SubstanceId);
        var referenceDetails = await _referenceRepository.GetReferenceById(referenceModel.ReferenceId);
        var sourceSystem = ((int) SourceSystem.PubMed).ToString(); 
        var synonyms = substanceDetails.SubstanceSynonyms.Select(x => x.Name).ToList();
        var referenceData = new PreClassifyReferenceCommand(referenceDetails.Title, referenceDetails.Abstract,
            referenceDetails.SourceId, sourceSystem, substanceDetails.Name, synonyms);
        await _aiReferencePublisher.Send(referenceData);
    }

    public async Task<AiSuggestedClassificationModel> GetExistingAiSuggestion(string sourceId, string substance)
    {
        var aiSuggestion = await _aiSuggestionRepository.GetBySourceIdAndSubstance(sourceId, substance);

        if (aiSuggestion != null)
            return new AiSuggestedClassificationModel
            {
                Id = aiSuggestion.Id,
                Status = (int)aiSuggestion.Status,
                Substance = aiSuggestion.Substance,
                Category = aiSuggestion.Category,
                CategoryReason = aiSuggestion.CategoryReason,
                DosageForm = aiSuggestion.DosageForm,
                DosageFormReason = aiSuggestion.DosageFormReason
            };

        return new AiSuggestedClassificationModel();
    }

    public async Task<IEnumerable<PreclassifyReferenceModel>> PickNextToPreClassify(int importId)
    {
        // When a user picks they should hold no locks. Enforce this just to be sure.
        var unlockingResult = await _referenceClassificationLockRepository.Unlock(_userContext.UserId);
        if (unlockingResult.ClassificationIds.Any())
        {
            _logger.LogWarning("PickNextToPreClassify: User {a} is picking but already holds locks. Locks released. unlockedClassificationIds={unlockedClassificationIds}",
                _userContext.UserId, string.Join(",", unlockingResult.ClassificationIds));
        }

        return await GetPreClassifyReferenceModelCollection(importId);
    }

    private async Task<IEnumerable<PreclassifyReferenceModel>> GetPreClassifyReferenceModelCollection(int importId)
    {
        // Need to know which classifications are locked by other users so we can pick something else.
        var allLockedClassificationIds = await _referenceClassificationLockRepository.GetAllLockedClassificationIdsForPick();

        ReferenceClassification referenceClassification = await GetClassification(importId, allLockedClassificationIds);

        if (referenceClassification != null)
        {
            var model = await BuildPreClassifyReferenceModel(referenceClassification);

            // Load duplicates with the same DOI as the picked classification - if it's an update then use the updated DOI
            var doi = model.ReferenceUpdate != null ? model.ReferenceUpdate.Doi : model.ReferenceClassification.Reference.Doi;
            var duplicates = await _referenceClassificationRepository.GetReferenceClassificationsForDuplicates(model.ReferenceClassification.Id, doi, model.ReferenceClassification.Substance.Id, allLockedClassificationIds);

            var preClassifyReferenceModels = new List<PreclassifyReferenceModel>
            {
                model
            };
            preClassifyReferenceModels.AddRange(await BuildPreClassifyReferenceModels(duplicates));

            // Lock the picked classifications
            var classificationIdsToLock = preClassifyReferenceModels.Select(e => e.ReferenceClassification.Id).ToList();
            var lockingResult = await _referenceClassificationLockRepository.Lock(classificationIdsToLock, _userContext.UserId);
            if (lockingResult.IsSuccessful)
            {
                _logger.LogInformation("PickNextToPreClassify: UserId={userId} picks and locks classificationIds={classificationIds}.",
                    _userContext.UserId, string.Join(",", lockingResult.ClassificationIds));

                preClassifyReferenceModels.Sort(new PreclassifyReferenceModelComparer());
                return preClassifyReferenceModels;
            }

            // NOTE: Can never get here as lockSuccess is always true (other case it exceptions)
            _logger.LogWarning("Failed to lock classificationIds={ids}, userId={userId}", string.Join(",", classificationIdsToLock), _userContext.UserId);
            return null;
        }

        _logger.LogWarning("User {a} failed to find any classifications to pick.", _userContext.UserId);

        return null;
    }

    private async Task<ReferenceClassification> GetClassification(int importId, List<int> allLockClassificationIds)
    {
        ReferenceClassification referenceClassification = await GetClassificationForSubstancePreference(importId, allLockClassificationIds);

        referenceClassification ??= await GetDefaultClassification(importId, allLockClassificationIds);
        return referenceClassification;
    }

    private async Task<PreclassifyReferenceModel> BuildPreClassifyReferenceModel(ReferenceClassification classification)
    {
        // Get Update + History Snapshot
        var classificationWithReferenceModel = _mapper.Map<ReferenceClassificationWithReferenceModel>(classification);
        var update = await _referenceUpdateRepository.GetForClassification(classification.ReferenceId, classification.SubstanceId);
        var snapshot = update != null ? await _referenceRepository.GetReferenceSnapshot(update.ReferenceId, update.CreatedDate) : null;

        var model = new PreclassifyReferenceModel
        {
            ReferenceClassification = classificationWithReferenceModel,
            ReferenceUpdate = update,
            ReferenceSnapshot = snapshot
        };

        if (await _featureManager.IsEnabledAsync(DisplayAiSuggestions))
        {
            model.AiSuggestedClassification = await GetAiSuggestedClassification(model);
        }

        return model;
    }

    private async Task<AiSuggestedClassificationModel> GetAiSuggestedClassification(PreclassifyReferenceModel model)
    {
        var dateRevised = model.ReferenceUpdate != null ? model.ReferenceUpdate.DateRevised : model.ReferenceClassification.Reference.DateRevised;

        // HACK: Manual Entry doesnt require the Date Revised to be entered (and we don't currently set to anything so we check that its not going to blow SQL up here)
        if (dateRevised == new DateTime(1, 1, 1, 0, 0, 0, DateTimeKind.Utc))
        {
            dateRevised = new DateTime(1980, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        }

        var sourceId = model.ReferenceUpdate != null
            ? model.ReferenceUpdate.SourceId
            : model.ReferenceClassification.Reference.SourceId;

        var substanceName = model.ReferenceClassification.Substance.Name;

        return await _referenceClassificationRepository.GetAiSuggestedClassification(dateRevised, substanceName, sourceId);
    }

    private async Task<List<PreclassifyReferenceModel>> BuildPreClassifyReferenceModels(IEnumerable<ReferenceClassification> classifications)
    {
        var list = new List<PreclassifyReferenceModel>();

        foreach (ReferenceClassification classification in classifications)
        {
            var model = await BuildPreClassifyReferenceModel(classification);
            list.Add(model);
        }

        return list;
    }

    private async Task<ReferenceClassification> GetDefaultClassification(int importId, List<int> allLockClassificationIds)
    {
        ReferenceClassification referenceClassification = await _referenceClassificationRepository.GetReferenceClassificationToPreClassify(importId, allLockClassificationIds);
        return referenceClassification;
    }

    private async Task<ReferenceClassification> GetClassificationForSubstancePreference(int importId, List<int> allLockClassificationIds)
    {
        return await _referenceClassificationRepository.GetReferenceClassificationToPreClassify_BySubstancePreference(_userContext.UserId, importId, allLockClassificationIds);
    }

    /// <remarks>
    /// In 655 branch this was one method called from the controller with a strategy injected on how to do the "pick" to populate "preClassifyReferenceModels"
    /// to avoid the boolean flag.
    /// That change wasn't reimplemented as the controller had changed so the two methods from the controller have simply been moved to this service
    /// and so the strategy injection had might as well be private and implemented here instead once this work is picked up again for the DataAccess work.
    /// </remarks>
    public async Task<PreclassifyModel> GetFirst()  // Pick_InitialPageLoad
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return new PreclassifyModel();
        }

        var preClassifyReferenceModels = await PickLockedClassifications();
        var preClassifyModelHelper = await _preClassifyModelHelper.GetPreClassifyModel(selectedImport, preClassifyReferenceModels);

        return preClassifyModelHelper;
    }

    public async Task<PreclassifyModel> GetNext()  // Pick_Subsequent
    {
        var selectedImport = await _dashboardService.GetSelectedImport();

        if (selectedImport == null)
        {
            return new PreclassifyModel();
        }

        var preClassifyReferenceModels = await PickNextToPreClassify(selectedImport.ImportId);
        var preClassifyModelHelper = await _preClassifyModelHelper.GetPreClassifyModel(selectedImport, preClassifyReferenceModels);

        return preClassifyModelHelper;
    }

    public async Task<bool> UserHasAllLocks(IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels)
    {
        var classificationIds = preClassifyReferenceModels.Select(x => x.ReferenceClassification.Id).ToList();
        var locksForUser = (await _referenceClassificationLockRepository.GetLocksForUser(_userContext.UserId)).ToList();
        var lockIds = locksForUser.Select(x => x.ReferenceClassificationId).ToList();
        var locklessClassificationIds = classificationIds.Except(lockIds).ToList();

        if (!locklessClassificationIds.Any())
        {
            return true;
        }

        var lockedClassificationIds = lockIds.Any() ? string.Join(",", lockIds) : "none";
        var updatedClassificationIds = string.Join(",", classificationIds);
        var locklessClassificationIdsMessage = string.Join(",", locklessClassificationIds);

        _logger.LogWarning("UserId={userId} cannot preclassify lockViolationClassificationIds={locklessClassificationIdsMessage} because they do not hold locks. " +
                           "lockedClassificationIds={lockedClassificationIds}; updatedClassificationIds={updatedClassificationIds}.",
            _userContext.UserId,
            locklessClassificationIdsMessage,
            lockedClassificationIds,
            updatedClassificationIds);
        return false;
    }

    public async Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetPreClassifiedByCurrentUser(int importId)
    {
        return await _referenceClassificationRepository.GetPreclassifiedByUserId(_userContext.UserId, importId);
    }

    public async Task RePreClassifyAsync(ReferenceClassificationModel model)
    {
        var classification = await _referenceClassificationRepository.GetByIdAsync(model.Id);

        if (classification == null)
        {
#pragma warning disable S112 // Legacy code
            throw new Exception($"Classification with id {model.Id} was not found");
#pragma warning restore S112
        }

        classification.RePreclassify(
            model.ClassificationCategoryId,
            model.DosageForm,
            model.CountryOfOccurrence);

        // Add Reference History Action - Pre-accessor edits classification from Completed tab
        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Classified);

        await _referenceClassificationRepository.SaveChangesAsync();
        await _referenceHistoryActionRepository.SaveChangesAsync();
    }

    public async Task Release()
    {
        await Release(_userContext.UserId);
    }

    public async Task Release(int userId)
    {
        var unlockingResult = await _referenceClassificationLockRepository.Unlock(userId);

        _logger.LogWarning("Release: User {a} released locks for user {b}. unlockedClassificationIds={unlockedClassificationIds}",
            _userContext.UserId, userId, string.Join(",", unlockingResult.ClassificationIds));
    }
}