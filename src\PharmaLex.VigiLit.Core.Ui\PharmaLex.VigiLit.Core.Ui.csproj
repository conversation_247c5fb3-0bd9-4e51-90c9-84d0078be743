<Project Sdk="Microsoft.NET.Sdk.Razor">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>
	<ItemGroup>
	  <Content Remove="Views\Shared\Components\ClassificationDisplay.cshtml" />
	  <Content Remove="Views\Shared\Components\ClassificationForm.cshtml" />
	  <Content Remove="Views\Shared\Components\ContractHistoryDisplay.cshtml" />
	  <Content Remove="Views\Shared\Components\DosageForm.cshtml" />
	  <Content Remove="Views\Shared\Components\ImageCell.cshtml" />
	  <Content Remove="Views\Shared\Components\ModalDialog.cshtml" />
	  <Content Remove="Views\Shared\Components\MultiSelect.cshtml" />
	  <Content Remove="Views\Shared\Components\RedirectDialog.cshtml" />
	  <Content Remove="Views\Shared\Components\ReferenceComponent.cshtml" />
	  <Content Remove="Views\Shared\Components\ReferenceInfo.cshtml" />
	  <Content Remove="Views\Shared\Components\SplitReferenceClassificationForm.cshtml" />
	  <Content Remove="Views\Shared\Components\Vue3\AutoCompleteList.cshtml" />
	  <Content Remove="Views\Shared\Components\Vue3\FilteredTable.cshtml" />
	  <Content Remove="Views\Shared\_Layout.cshtml" />
	  <Content Remove="Views\_ViewImports.cshtml" />
	  <Content Remove="Views\_ViewStart.cshtml" />
	</ItemGroup>
	<ItemGroup>
	  <EmbeddedResource Include="Views\Shared\Components\ClassificationDisplay.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ClassificationForm.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ContractHistoryDisplay.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\DosageForm.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ImageCell.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ModalDialog.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\MultiSelect.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\RedirectDialog.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ReferenceComponent.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\ReferenceInfo.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\SplitReferenceClassificationForm.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\Vue3\AutoCompleteList.cshtml" />
	  <EmbeddedResource Include="Views\Shared\Components\Vue3\FilteredTable.cshtml" />
	  <EmbeddedResource Include="Views\Shared\_Layout.cshtml" />
	  <EmbeddedResource Include="Views\_ViewImports.cshtml" />
	  <EmbeddedResource Include="Views\_ViewStart.cshtml" />
	</ItemGroup>
	<ItemGroup>
		<SupportedPlatform Include="browser" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.13" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
	</ItemGroup>
</Project>