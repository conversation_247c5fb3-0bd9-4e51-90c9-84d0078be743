﻿using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;
public class FailedImportFileModel : IViewModel
{

#pragma warning disable S6964
    public int Id { get; set; }
#pragma warning restore S6964
    [Required(ErrorMessage = "Abstract is required.")]
    public string? Abstract { get; set; }
    public string? AffiliationTextFirstAuthor { get; set; }
    public string? Authors { get; set; }
    [Required(ErrorMessage = "Country of occurrence is required.")]
    public string? CountryOfOccurrence { get; set; }
    public string? Doi { get; set; }
    public string? FullPagination { get; set; }
    public string? Issn { get; set; }
    public string? Issue { get; set; }
    public string? Language { get; set; }
    [JsonRequired]
    public int SourceSystem { get; set; }
    public string? PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    [Required(ErrorMessage = "Article Title is required.")]
    public string? Title { get; set; }
    public string? Volume { get; set; }
    public string? Keywords { get; set; }
    [Required(ErrorMessage = "Journal Title is required.")]
    public string? JournalTitle { get; set; }
    public string? Filename { get; set; }
    public ImportReferenceStatusType? StatusType { get; set; }

    [Required(ErrorMessage = "BatchId is required.")]
    public Guid? BatchId { get; set; }
    [JsonRequired]
    public bool AbstractConfidenceCheckPassed { get; set; }
    [JsonRequired]
    public bool CountryOfOccurrenceConfidenceCheckPassed { get; set; }
    [JsonRequired]
    public bool TitleConfidenceCheckPassed { get; set; }
    [JsonRequired]
    public bool JournalTitleConfidenceCheckPassed { get; set; }
}
