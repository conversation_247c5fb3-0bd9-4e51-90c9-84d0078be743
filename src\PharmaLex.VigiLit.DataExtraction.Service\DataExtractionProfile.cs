﻿using AutoMapper;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal class DataExtractionProfile : Profile
{
    public static ReferenceDto Copy(ExtractedReference source)
    {
        ArgumentNullException.ThrowIfNull(source);

        var destination = new ReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            Language = "LANGUAGE",
            SourceSystem = (int)SourceSystem.Web,
            SourceId = "SOURCE ID",
            PublicationType = "PUBLICATION TYPE",
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = "VOLUME ABBREVIATION",
            Keywords = source.Keywords.Value,
            MeshHeadings = string.Empty,
            JournalTitle = source.JournalTitle.Value,
        };
        return destination;
    }
    public static FailedImportReferenceDto CopyFailed(ExtractedReference source, float minMandatoryConfidenceLevel)
    {
        ArgumentNullException.ThrowIfNull(source);

        var destination = new FailedImportReferenceDto()
        {
            Abstract = source.Abstract.Value,
            AffiliationTextFirstAuthor = source.Affiliations[0].Value,
            Authors = string.Join(",", source.Authors.Select(a => a.Value).ToArray()),
            CountryOfOccurrence = source.CountryOfOccurrence.Value,
            Doi = source.Doi.Value,
            FullPagination = source.Pages.Value,
            Issn = source.Issn.Value,
            Language = "LANGUAGE",
            SourceSystem = (int)SourceSystem.Web,
            SourceId = "SOURCE ID",
            PublicationType = "PUBLICATION TYPE",
            PublicationYear = ushort.TryParse(source.Year?.Value, out var year) ? year : null,
            Title = source.Title.Value,
            Volume = source.Volume.Value,
            VolumeAbbreviation = "VOLUME ABBREVIATION",
            Keywords = source.Keywords.Value,
            MeshHeadings = string.Empty,
            JournalTitle = source.JournalTitle.Value,

            AbstractConfidence = ToByteOrDefault(source.Abstract?.Confidence),
            AffiliationTextFirstAuthorConfidence = ToByteOrDefault(source.Affiliations?.FirstOrDefault()?.Confidence),
            AuthorsConfidence = ToByteOrDefault(source.Authors?.FirstOrDefault()?.Confidence),
            CountryOfOccurrenceConfidence = ToByteOrDefault(source.CountryOfOccurrence?.Confidence),
            DoiConfidence = ToByteOrDefault(source.Doi?.Confidence),
            FullPaginationConfidence = ToByteOrDefault(source.Pages?.Confidence),
            IssnConfidence = ToByteOrDefault(source.Issn?.Confidence),
            IssueConfidence = ToByteOrDefault(source.IssueNumber?.Confidence),
            PublicationYearConfidence = ToByteOrDefault(source.Year?.Confidence),
            TitleConfidence = ToByteOrDefault(source.Title?.Confidence),
            VolumeConfidence = ToByteOrDefault(source.Volume?.Confidence),
            KeywordsConfidence = ToByteOrDefault(source.Keywords?.Confidence),
            JournalTitleConfidence = ToByteOrDefault(source.JournalTitle?.Confidence),
            //DateRevisedConfidence = 0,// NOSONAR
            //LanguageConfidence = 0,// NOSONAR
            //SourceSystemConfidence = 0,// NOSONAR
            //VolumeAbbreviationConfidence = 0;// NOSONAR

            AbstractConfidenceCheckPassed = CheckIfPassesConfidence(source.Abstract, minMandatoryConfidenceLevel),
            TitleConfidenceCheckPassed = CheckIfPassesConfidence(source.Title, minMandatoryConfidenceLevel),
            JournalTitleConfidenceCheckPassed = CheckIfPassesConfidence(source.JournalTitle, minMandatoryConfidenceLevel),
            CountryOfOccurrenceConfidenceCheckPassed = CheckIfPassesConfidence(source.CountryOfOccurrence, minMandatoryConfidenceLevel)
        };
        return destination;
    }

    private static byte ToByteOrDefault(float? input) =>
      input.HasValue ? (byte)(input.Value  * 100): (byte)0;

    public static bool CheckIfPassesConfidence(IExtractedProperty? property, float threshold)
    {
        return property != null && property.Confidence >= threshold;
    }
}