﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ContractManagement.Enums;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public class JournalRepository : TrackingGenericRepository<Journal>, IJournalRepository
{
    public JournalRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }
    public async Task<IEnumerable<Journal>> GetAll()
    {
        return await context.Set<Journal>()
            .Include(ic => ic.Country)
            .OrderBy(c => c.Name)
            .AsNoTracking()
            .ToListAsync();
    }
    public async Task<IEnumerable<Journal>> GetForCountry(int countryId)
    {
        return await context.Set<Journal>()
            .Where(u => u.CountryId == countryId)
            .ToListAsync();
    }

    public async Task<Journal?> GetById(int id)
    {
        return await context.Set<Journal>()
            .Where(u => u.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Country>> GetSubscribedCountries()
    {
        return await context.Set<Journal>()
            .Where(j => j.Enabled)
            .Select(j => j.Country)
            .Distinct()
            .ToListAsync();
    }
    public async Task<int> GetContractsWithEnabledJournalById(int journalId)
    {
        var count = await context.Set<ContractVersion>()
            .Include(cv => cv.ContractVersionJournals)
                .ThenInclude(cvj => cvj.Journal)
            .Include(cv => cv.Contract)
                .ThenInclude(c => c.Project)
                    .ThenInclude(p => p.Company)
            .Where(cv =>
                cv.IsActive
                && cv.ContractVersionStatus == ContractVersionStatus.Approved
                && cv.Contract.Project.Company.IsActive
                && cv.Contract.ScreeningType == (int)ScreeningType.Local
                && cv.ContractVersionJournals.Any(cvj => cvj.JournalId == journalId && cvj.Journal.Enabled)
            )
            .CountAsync();

        return count;
    }

    public async Task UpdateLastRun(int journalId, DateTime lastRunTime)
    {
        var journal = await context.Set<Journal>()
            .Where(j => j.Id == journalId)
            .FirstOrDefaultAsync();

        if (journal != null)
        {
            journal.LastRun = lastRunTime;
            await SaveChangesAsync();
        }
    }
}