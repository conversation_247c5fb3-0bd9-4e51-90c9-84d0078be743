﻿using PharmaLex.VigiLit.ImportManagement.Enums;
namespace PharmaLex.VigiLit.ImportManagement.Ui.Models;
public class ImportReferenceModel
{

#pragma warning disable S6964
    public int Id { get; set; }
#pragma warning restore S6964

    public string? Abstract { get; set; }
    public string? AffiliationTextFirstAuthor { get; set; }
    public string? Authors { get; set; }
    public string? CountryOfOccurrence { get; set; }
    public string? Doi { get; set; }
    public string? FullPagination { get; set; }
    public string? Issn { get; set; }
    public string? Issue { get; set; }
    public string? Language { get; set; }
    public int? SourceSystem { get; set; }
    public string? PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    public string? Title { get; set; }
    public string? Volume { get; set; }
    public string? Keywords { get; set; }
    public string? JournalTitle { get; set; }
    public ImportReferenceStatusType? StatusType { get; set; }
}
